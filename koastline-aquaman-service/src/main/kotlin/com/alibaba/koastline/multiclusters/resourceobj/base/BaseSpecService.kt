package com.alibaba.koastline.multiclusters.resourceobj.base

import com.alibaba.koastline.multiclusters.apre.base.MetadataUtils
import com.alibaba.koastline.multiclusters.apre.params.MetadataStageEnum
import com.alibaba.koastline.multiclusters.common.config.CommonProperties
import com.alibaba.koastline.multiclusters.external.CloudCmdbApi
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import io.kubernetes.client.openapi.models.V1Affinity
import io.kubernetes.client.openapi.models.V1Container
import io.kubernetes.client.openapi.models.V1EnvVar
import io.kubernetes.client.openapi.models.V1NodeAffinity
import io.kubernetes.client.openapi.models.V1NodeSelector
import io.kubernetes.client.openapi.models.V1NodeSelectorRequirement
import io.kubernetes.client.openapi.models.V1NodeSelectorTerm
import io.kubernetes.client.openapi.models.V1ObjectMeta
import io.kubernetes.client.openapi.models.V1PodSpec
import io.kubernetes.client.openapi.models.V1PodTemplateSpec
import io.kubernetes.client.openapi.models.V1Toleration
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.util.Locale
import java.util.UUID

/**
 * 昆仑
 *
 */
@Component
class BaseSpecService {
    @Autowired
    lateinit var cloudCmdbApi: CloudCmdbApi
    @Autowired
    lateinit var commonProperties: CommonProperties



    /**
     * 缺省的六元组，系统参数，强制覆盖用户编排同名属性
     * @param workloadMetadataConstraint
     * @return
     */
    fun getDefaultLabels(workloadMetadataConstraint: WorkloadMetadataConstraint): Map<String, String> {
        val commonMetaDataLabels: MutableMap<String, String> = HashMap()
        commonMetaDataLabels[INPLACESET_LABEL_APPNAME] = workloadMetadataConstraint.appName
        commonMetaDataLabels[INPLACESET_LABEL_NODEGROUP] = workloadMetadataConstraint.resourceGroup
        commonMetaDataLabels[INPLACESET_LABEL_SITE] = workloadMetadataConstraint.site
        commonMetaDataLabels[INPLACESET_LABEL_UNIT] = checkNotNull(MetadataUtils.formatUnit(workloadMetadataConstraint.unit))
        commonMetaDataLabels[INPLACESET_LABEL_USETYPE] = workloadMetadataConstraint.stage
        commonMetaDataLabels[INPLACESET_LABEL_SUBGROUP] = "default"
        workloadMetadataConstraint.runtimeId?.run {
            commonMetaDataLabels[RUNTIME_ID] = this
        }
        return commonMetaDataLabels
    }

    fun buildWorkloadName(appName: String): String {
        val ns = WorkloadUtils.buildNamespace(appName).run {
            if (this.length > INPLACESET_APPNAME_NAME_SIZE) {
                this.substring(0, INPLACESET_APPNAME_NAME_SIZE)
            } else {
                this
            }
        }
        val uuid = UUID.randomUUID().toString().run {
            val needUuidNum = INPLACESET_NAME_SIZE - String.format("%s---%s", ns,
                INPLACESET_NAME_SUFFIX
            ).length
            if (needUuidNum < this.length) {
                this.substring(0, needUuidNum)
            } else {
                this
            }
        }
        return String.format("%s-%s--%s", ns, uuid, INPLACESET_NAME_SUFFIX)
    }

    /**
     * 获取主机名模板
     */
    fun getPodHostNameFormat(workloadMetadataConstraint: WorkloadMetadataConstraint): String {
        val appName: String = WorkloadUtils.buildNamespace(workloadMetadataConstraint.appName)
        return when (workloadMetadataConstraint.stage) {
            MetadataStageEnum.PUBLISH.name, MetadataStageEnum.GRAY.name -> generatePodHostNameFormat(workloadMetadataConstraint.appName, MetadataUtils.formatShortUnit(workloadMetadataConstraint.unit), workloadMetadataConstraint.site, true)
            MetadataStageEnum.PRE_PUBLISH.name -> generatePodHostNameFormat(appName, "pre", workloadMetadataConstraint.site, true)
            MetadataStageEnum.SMALLFLOW.name -> generatePodHostNameFormat(appName, "test", workloadMetadataConstraint.site, true)
            else -> generatePodHostNameFormat(appName, null, workloadMetadataConstraint.site, false)
        }
    }

    private fun generatePodHostNameFormat(appName: String, unit: String?, site: String, containUnit: Boolean): String {
        val format = if (containUnit && !unit.isNullOrBlank() && unit.length <= HOST_NAME_UNIT_TAG_LENGTH_LIMIT) {
            String.format("%s{{.IpAddress}}.%s.%s", appName, unit, site.lowercase(Locale.getDefault()))
        } else {
            String.format("%s{{.IpAddress}}.%s", appName, site.lowercase(Locale.getDefault()))
        }
        return format.replace("_", "-")
    }

    /**
     * 设置pod meta data
     */
    fun setPodMetadata(podTemplateSpec: V1PodTemplateSpec, workloadMetadataConstraint: WorkloadMetadataConstraint) {
        (podTemplateSpec.metadata ?: V1ObjectMeta().apply { podTemplateSpec.metadata = this }).let { metadata ->
            (metadata.labels ?: mutableMapOf<String, String>().apply{ metadata.labels = this}).let { labels ->
                //六元组覆盖
                labels.putAll(getDefaultLabels(workloadMetadataConstraint))
                labels[POD_CONTAINER_MODEL] = labels[POD_CONTAINER_MODEL] ?: kotlin.run { "dockervm" }
                labels[POD_UPSTREAM_COMPONENT] = labels[POD_UPSTREAM_COMPONENT] ?: kotlin.run { NORMANDY }
                labels[POD_RIGISTER_STATE] = labels[POD_RIGISTER_STATE] ?: kotlin.run { "working_online" }
            }
            (metadata.annotations ?: mutableMapOf<String, String>().apply { metadata.annotations = this }).let { annotations ->
                annotations[ALARMING_OFF_UPGRADE] = annotations[ALARMING_OFF_UPGRADE] ?: kotlin.run { "true" }
                annotations[ENABLE_APPRULES_INJECTION] = annotations[ENABLE_APPRULES_INJECTION] ?: kotlin.run { "true" }
                annotations[USE_UNIFIED_PV] = annotations[USE_UNIFIED_PV] ?: kotlin.run { "true" }
                annotations[CONTAINER_EXTRA_CONFIG] = annotations[CONTAINER_EXTRA_CONFIG] ?: kotlin.run { "{\"containerConfigs\":{\"main\":{\"PostStartHookTimeoutSeconds\":\"1800\",\"ImagePullTimeoutSeconds\":\"600\",\"PreStopHookTimeoutSeconds\":\"600\"}}}" }
                annotations[HOST_NAME_TEMP] = annotations[HOST_NAME_TEMP] ?: kotlin.run { getPodHostNameFormat(workloadMetadataConstraint) }
                annotations[IP_STACK] = annotations[IP_STACK] ?: kotlin.run { "ipv4" }
            }
        }
    }

    /**
     * 兼容老逻辑设置 spec.dnsPolicy
     */
    fun setDnsPolicy(podSpec: V1PodSpec) {
        podSpec.dnsPolicy = podSpec.dnsPolicy ?: kotlin.run { "default" }
    }

    /**
     * 设置缺省亲和策略
     */
    fun setDefaultAffinity(podSpec: V1PodSpec, clusterId: String) {
        ((((podSpec.affinity ?: V1Affinity().apply { podSpec.affinity = this })
            .nodeAffinity ?: V1NodeAffinity().apply { podSpec.affinity!!.nodeAffinity = this })
            .requiredDuringSchedulingIgnoredDuringExecution ?: V1NodeSelector().apply { podSpec.affinity!!.nodeAffinity!!.requiredDuringSchedulingIgnoredDuringExecution = this })
            .nodeSelectorTerms ?: mutableListOf<V1NodeSelectorTerm>().apply { podSpec.affinity!!.nodeAffinity!!.requiredDuringSchedulingIgnoredDuringExecution!!.nodeSelectorTerms = this }).let { nodeSelectorTerms ->
            nodeSelectorTerms.add(
                V1NodeSelectorTerm().apply {
                    //缺省资源池
                    this.addMatchExpressionsItem(
                        V1NodeSelectorRequirement().apply {
                            this.key = AFFINITY_NODE_SELECTOR_KEY_RESOURCE_POOL
                            this.operator= "In"
                            this.values = listOf("sigma_public")
                        }
                    )
                    //添加默认亲和ECS，物理机集群不添加
                    this.addMatchExpressionsItem(
                        V1NodeSelectorRequirement().apply {
                            this.key = AFFINITY_NODE_SELECTOR_KEY_IS_ECS
                            this.operator= "In"
                            this.values = listOf("true")
                        }
                    )
                }
            )
        }
    }

    /**
     * 测试/预发环境设置隔离标（如存在）
     */
    fun setIsolationEnvVars(containers: List<V1Container>, stackId: String, stage: String) {
        if (!listOf(MetadataStageEnum.PRE_PUBLISH.name, MetadataStageEnum.DAILY.name).contains(stage)) {
            return;
        }
        val envVars = cloudCmdbApi.getIsolationEnvVars(stackId).toMutableMap()
        if (MetadataStageEnum.DAILY.name == stage) {
            envVars[CloudCmdbApi.SCM_HOOK_KEY] = CloudCmdbApi.SCM_HOOK_ENABLE_VALUE
        }
        containers.first { container ->
            container.name == "main"
        }.let { container ->
            envVars.forEach{ envVar ->
                container.env ?.firstOrNull {
                    envVar.key == it.name
                } ?:let {
                    container.addEnvItem(V1EnvVar().apply {
                        this.name = envVar.key
                        this.value = envVar.value
                    })
                }
            }
        }
    }

    fun patchAffinityAndTolerations(podTemplateSpec: V1PodTemplateSpec) {
        //默认移除 is-ecs affinity 并添加 is-ecs toleration
        podTemplateSpec.spec?.affinity?.nodeAffinity?.requiredDuringSchedulingIgnoredDuringExecution?.nodeSelectorTerms?.forEach { nodeSelectorTerm ->
            nodeSelectorTerm.matchExpressions = nodeSelectorTerm.matchExpressions?.filter {
                it.key != AFFINITY_NODE_SELECTOR_KEY_IS_ECS
            }
        }
        val tolerationsToAddEcs =
            (podTemplateSpec.spec?.tolerations ?: mutableListOf()).apply { podTemplateSpec.spec?.tolerations = this }
        if (tolerationsToAddEcs.none {
                it.key == AFFINITY_NODE_SELECTOR_KEY_IS_ECS && it.operator == "Exists"
            }) {
            tolerationsToAddEcs.add(
                V1Toleration().apply {
                    this.key = AFFINITY_NODE_SELECTOR_KEY_IS_ECS
                    this.operator = "Exists"
                }
            )
        }

    }

    /**
     * 物理机集群建站链路设置
     */
    fun modifyForPhysicalMachineCluster(podTemplateSpec: V1PodTemplateSpec, workloadMetadataConstraint: WorkloadMetadataConstraint) {
        if (commonProperties.contains(
                CommonProperties.ASI_LOCAL_INLINE_STORAGE_SITE_LIST_CONFIG,
                workloadMetadataConstraint.site
            )

        ) {
            podTemplateSpec.metadata!!.annotations!!.let {
                it[APP_STORAGE_MODE] = "local-inline"
                it.remove(USE_UNIFIED_PV)
            }
            return
        }
        if (!commonProperties.contains(CommonProperties.ASI_PHYSICAL_CLUSTER_NAME_LIST_CONFIG, workloadMetadataConstraint.clusterId)) {
            return
        }
        //affinity要去掉sigma.ali/is-ecs
        val nodeSelector = checkNotNull(podTemplateSpec.spec ?.affinity ?.nodeAffinity ?.requiredDuringSchedulingIgnoredDuringExecution) {"nodeSelector must not be null."}
        nodeSelector.nodeSelectorTerms.forEach { nodeSelectorTerm ->
            nodeSelectorTerm.matchExpressions = nodeSelectorTerm.matchExpressions ?.filter {
                it.key != AFFINITY_NODE_SELECTOR_KEY_IS_ECS
            }
        }

        //采用本地盘，spec.template.metadata.annotations添加annotation
        //移除sigma.ali/use-unified-pv
        podTemplateSpec.metadata!!.annotations!!.let {
            it[APP_STORAGE_MODE] = "local-inline"
            it.remove(USE_UNIFIED_PV)
        }
    }

    /**
     * 离在线混部分时扩容场景,更新Label & Annotation
     */
    fun modifyForMixDeployCluster(podTemplateSpec: V1PodTemplateSpec, workloadMetadataConstraint: WorkloadMetadataConstraint) {
        if (!commonProperties.contains(CommonProperties.ASI_MIX_CLUSTER_NAME_LIST_CONFIG, workloadMetadataConstraint.clusterId)) {
            return
        }
        //添加annotation
        podTemplateSpec.metadata!!.annotations!!.let {
            it[SKIP_KUBELET_ADMISSION] = "[\"cpu\",\"memory\",\"alibabacloud.com/acu\"]"
        }
        //添加label
        podTemplateSpec.metadata!!.labels!!.let {
            it[TIMESHARING_PROMOTION_TYPE] = "trade"
        }
    }

    companion object {
        //labels
        const val INPLACESET_LABEL_SITE = "sigma.ali/site"
        const val INPLACESET_LABEL_APPNAME = "sigma.ali/app-name"
        const val INPLACESET_LABEL_NODEGROUP = "sigma.ali/instance-group"
        const val INPLACESET_LABEL_UNIT = "sigma.alibaba-inc.com/app-unit"
        const val INPLACESET_LABEL_USETYPE = "sigma.alibaba-inc.com/app-stage"
        const val INPLACESET_LABEL_SUBGROUP = "sigma.ali/subgroup"
        const val RUNTIME_ID                = "sigma.ali/runtime-id"
        val INPLACESET_BLACK_LABEL_LIST = listOf(
            INPLACESET_LABEL_SITE,
            INPLACESET_LABEL_APPNAME,
            INPLACESET_LABEL_NODEGROUP,
            INPLACESET_LABEL_UNIT,
            INPLACESET_LABEL_USETYPE,
            INPLACESET_LABEL_SUBGROUP,
            RUNTIME_ID,
        )
        const val UPGRADE_MERGE_LABELS = "sigma.ali/upgrade-merge-labels"
        const val UPGRADE_MERGE_ANNOS = "sigma.ali/upgrade-merge-annotations"

        /**
         * 代表POD升级从Workload继承的labels
         */
        const val UPGRADE_INHERITED_LABELS = "sigma.ali/upgrade-inherited-labels"

        val INPLACESET_WHITE_WORKLOAD_ANNO_LIST = listOf(
            UPGRADE_MERGE_LABELS,
            UPGRADE_MERGE_ANNOS,
            UPGRADE_INHERITED_LABELS,
        )
        const val LAST_POD_TEMPLATE_HASH = "normandy.alibabacloud.com/last-pod-template-hash"
        const val POD_TEMPLATE_HASH = "normandy.alibabacloud.com/pod-template-hash"
        const val ROLLBACKINDEPLOY_ALARM = "normandy.alibabacloud.com/rollbackInDeploy-alarm"
        val INPLACESET_WHITE_WORKLOAD_LABEL_LIST = listOf(
            LAST_POD_TEMPLATE_HASH,
            POD_TEMPLATE_HASH,
        )
        const val INPLACESET_ENABLE_POD_IDX_GEN = "cloneset.beta1.sigma.ali/enable-pod-idx-gen"
        /**
         * POD标签-部署版本
         */
        const val LABEL_DEPLOY_STACK_ID = "tags.aone.alibaba-inc.com/deploy-stack-id"

        /**
         * POD标签-环境等级
         */
        const val LABEL_ENV_LEVEL = "tags.aone.alibaba-inc.com/env-level"

        /**
         * 交付版本
         */
        const val LABEL_APP_DEPLOY_VERSION = "tags.sunfire.com/app-deploy-version"
        val INPLACESET_WHITE_BASE_WORKLOAD_LABEL_LIST = listOf(
            INPLACESET_ENABLE_POD_IDX_GEN,
            LABEL_DEPLOY_STACK_ID,
            LABEL_ENV_LEVEL,
            LABEL_APP_DEPLOY_VERSION,
        )
        const val INPLACESET_LABEL_STACKID = "normandy.alibaba-inc.com/stack-id"
        val TEMPLATE_LABEL_BLACK_LIST = listOf(INPLACESET_LABEL_SITE, INPLACESET_LABEL_APPNAME, INPLACESET_LABEL_NODEGROUP, INPLACESET_LABEL_UNIT, INPLACESET_LABEL_USETYPE, INPLACESET_LABEL_STACKID)
        const val DISABLE_CASCADING_DELETION = "sigma.ali/disable-cascading-deletion"
        const val LAST_APPLIED_CONFIGURATION = "kubectl.kubernetes.io/last-applied-configuration"

        //pod labels
        const val POD_CONTAINER_MODEL = "sigma.ali/container-model"
        const val POD_UPSTREAM_COMPONENT = "sigma.ali/upstream-component"
        const val NORMANDY = "normandy"
        const val POD_RIGISTER_STATE = "pod.beta1.sigma.ali/naming-register-state"
        const val TIMESHARING_PROMOTION_TYPE = "alibabacloud.com/timesharing-promotion-type"

        //pod annotations
        const val ALARMING_OFF_UPGRADE = "pod.beta1.sigma.ali/alarming-off-upgrade"
        const val ENABLE_APPRULES_INJECTION = "sigma.ali/enable-apprules-injection"
        const val USE_UNIFIED_PV = "sigma.ali/use-unified-pv";
        const val CONTAINER_EXTRA_CONFIG = "pod.beta1.sigma.ali/container-extra-config"
        const val IP_STACK = "alibabacloud.com/ip-stack"
        const val APP_STORAGE_MODE = "sigma.ali/app-storage-mode"
        const val APP_STORAGE_SIZE = "sigma.ali/app-storage-size"
        const val SKIP_KUBELET_ADMISSION = "alibabacloud.com/skip-kubelet-admission"

        //affinity
        const val AFFINITY_NODE_SELECTOR_KEY_RESOURCE_POOL = "sigma.ali/resource-pool"
        const val AFFINITY_NODE_SELECTOR_KEY_IS_ECS = "sigma.ali/is-ecs"

        const val PHYSICAL_CLUSTER_LOCAL_INLINE_STORAGE= 60;

        //pod annotations
        const val HOST_NAME_TEMP = "pod.beta1.sigma.ali/hostname-template"

        private const val INPLACESET_NAME_SUFFIX = "n3";
        private const val MINIMAL_UUID_LENGTH = 5
        private const val INPLACESET_APPNAME_NAME_SIZE = 42 - INPLACESET_NAME_SUFFIX.length - MINIMAL_UUID_LENGTH
        private const val INPLACESET_NAME_SIZE = 45

        private const val HOST_NAME_UNIT_TAG_LENGTH_LIMIT = 10
    }
}