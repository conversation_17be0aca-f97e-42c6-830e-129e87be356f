package com.alibaba.koastline.multiclusters.resourceobj

import com.alibaba.koastline.multiclusters.appenv.model.Constants.IS_DELETED
import com.alibaba.koastline.multiclusters.apre.common.MatchScopeService
import com.alibaba.koastline.multiclusters.apre.common.MatchScopeService.Companion.ALIBABA_GROUP
import com.alibaba.koastline.multiclusters.apre.model.MatchScopeDataDO
import com.alibaba.koastline.multiclusters.apre.model.Restriction
import com.alibaba.koastline.multiclusters.apre.model.equalsIgnoreOrder
import com.alibaba.koastline.multiclusters.apre.model.req.ActionLogsReqDto
import com.alibaba.koastline.multiclusters.apre.model.req.MatchScopeDataReqDto
import com.alibaba.koastline.multiclusters.apre.params.ActionEnum
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeTargetTypeEnum
import com.alibaba.koastline.multiclusters.common.config.CommonProperties
import com.alibaba.koastline.multiclusters.common.config.CommonProperties.Companion.LOW_RANK_MODIFIER
import com.alibaba.koastline.multiclusters.common.exceptions.MatchScopeDataException
import com.alibaba.koastline.multiclusters.common.exceptions.ResourceObjectException
import com.alibaba.koastline.multiclusters.common.exceptions.ResourceObjectProtocolNotFoundException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.FreemarkerUtils
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils.toJson
import com.alibaba.koastline.multiclusters.common.utils.KeyGenerator
import com.alibaba.koastline.multiclusters.common.utils.YamlUtils
import com.alibaba.koastline.multiclusters.common.utils.toNullIfBlank
import com.alibaba.koastline.multiclusters.data.dao.env.MatchScopeDataRepo
import com.alibaba.koastline.multiclusters.data.dao.resourceobj.ActionLogRepo
import com.alibaba.koastline.multiclusters.data.dao.resourceobj.ResourceObjectFeatureImportRepo
import com.alibaba.koastline.multiclusters.data.dao.resourceobj.ResourceObjectFeatureProtocolRepo
import com.alibaba.koastline.multiclusters.data.dao.resourceobj.ResourceObjectFeatureRepo
import com.alibaba.koastline.multiclusters.data.vo.NextTokenData
import com.alibaba.koastline.multiclusters.data.vo.PageData
import com.alibaba.koastline.multiclusters.data.vo.env.*
import com.alibaba.koastline.multiclusters.external.*
import com.alibaba.koastline.multiclusters.external.model.AppInfo
import com.alibaba.koastline.multiclusters.resourceobj.hook.FeatureImportHookManager
import com.alibaba.koastline.multiclusters.resourceobj.model.*
import com.alibaba.koastline.multiclusters.resourceobj.model.req.*
import com.alibaba.koastline.multiclusters.resourceobj.params.*
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectConstants.CPUSHARE_FEATURE_KEY
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectConstants.ENV_LABELS_FEATURE_KEY
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectConstants.LIGHT_CONTAINER_FEATURE_KEY
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectConstants.RESOURCE_OBJECT_FEATURE_SCOPE
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectConstants.RESOURCE_SPEC_FEATURE_KEY
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectConstants.SAFETY_OUT
import com.alibaba.koastline.multiclusters.resourceobj.specific.ResourceSpecFeatureService
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import com.alibaba.koastline.multiclusters.schedule.service.schedule.DeclarativeScaleOutScheduleService.Companion.TEST_STAGE
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.github.pagehelper.Page
import com.github.pagehelper.PageHelper
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.Instant


@Component
class ResourceObjectFeatureService(val objectMapper: ObjectMapper) {
    @Autowired
    lateinit var resourceObjectFeatureRepo: ResourceObjectFeatureRepo

    @Autowired
    lateinit var resourceObjectFeatureImportRepo: ResourceObjectFeatureImportRepo

    @Autowired
    lateinit var resourceObjectFeatureProtocolRepo: ResourceObjectFeatureProtocolRepo

    @Autowired
    lateinit var resourceSpecFeatureService: ResourceSpecFeatureService

    @Autowired
    lateinit var matchScopeService: MatchScopeService

    @Autowired
    lateinit var matchScopeDataRepo: MatchScopeDataRepo

    @Autowired
    lateinit var actionLogRepo: ActionLogRepo

    @Autowired
    lateinit var astroApi: AstroApi

    @Autowired
    lateinit var skylineApi: SkylineApi

    @Autowired
    lateinit var userLabelService: UserLabelService

    @Autowired
    lateinit var appCenterApi: AppCenterApi

    @Autowired
    lateinit var gropApi: GropApi

    @Autowired
    lateinit var jmenvApi: JmenvApi

    @Autowired
    lateinit var featureProtocolLoadService: FeatureProtocolLoadService

    @Autowired
    lateinit var commonProperties: CommonProperties

    @Autowired
    lateinit var featureImportHookManager: FeatureImportHookManager

    @Autowired
    lateinit var dispatchLabelService: DispatchLabelService

    @Autowired
    lateinit var kvMapService: KvMapService

    private val logger by logger()

    /**
     * 根据七元组对应的注入特性列表
     * 注：相同的特性注入（可能参数不同）基于最小范围优先原则去重
     */
    fun listResourceObjectFeatureImportSpec(
        workloadMetadataConstraint: WorkloadMetadataConstraint,
        resourceObjectSceneEnum: ResourceObjectSceneEnum,
        resourceObjectBuildTypeEnum: ResourceObjectBuildTypeEnum,
        protocol: String,
        version: String?,
        systemInputParams: Map<String, Any>,
        envStackId: String
    ): List<FeatureImportPatchDO> {
        // 支持到全局/产品线/应用/分组维度获取开启特性
        val featureMap = addDefaultFeatureSpec(
            extractFeatureMap(workloadMetadataConstraint, envStackId, systemInputParams),
            workloadMetadataConstraint,
            protocol
        )
        // 按照特性导入场景过滤
        val filteredResourceObjectFeatureImportList = filteredResourceObjectFeatureImportByUseScope(
            featureMap = featureMap,
            resourceObjectSceneEnum = resourceObjectSceneEnum,
            resourceObjectBuildTypeEnum = resourceObjectBuildTypeEnum,
            protocol = protocol
        )
        logger.info("filteredResourceObjectFeatureImportList: ${JsonUtils.writeValueAsString(filteredResourceObjectFeatureImportList)}")
        //根据模板生成特性Spec
        return assembleSpecByFeatureImport(
            resourceObjectFeatureImportList = filteredResourceObjectFeatureImportList,
            protocol = protocol,
            version = version,
            systemInputParams = systemInputParams + ("MW_ENV" to jmenvApi.getMiddlewareEnv(
                workloadMetadataConstraint.stage,
                workloadMetadataConstraint.unit
            )),
            workloadMetadataConstraint = workloadMetadataConstraint,
        )
    }

    fun listVersionFeatureImportSpec(
        appName: String,
        resourceObjectSceneEnum: ResourceObjectSceneEnum,
        resourceObjectBuildTypeEnum: ResourceObjectBuildTypeEnum,
        protocol: String,
        version: String?,
        userTraits: List<UserTrait>,
        systemInputParams: Map<String, Any>,
        envStackId: String
    ): List<FeatureImportPatchDO> {
        val filteredVersionFeatureImportList: List<ResourceObjectFeatureImport>
        val crFeatureImportList = userTraits.map {
            ResourceObjectFeatureImport(
                resourceObjectFeatureKey = it.key,
                paramMap = YamlUtils.dump(it.content),
                status = "ENABLE",
                creator = it.modifier,
                modifier = it.modifier,
                gmtCreate = java.util.Date(Instant.now().toEpochMilli()),
                gmtModified = java.util.Date(Instant.now().toEpochMilli()),
                version = it.version ?: DEFAULT_VERSION,
            )
        }
        val featureMap = extractVersionTraitMap(appName, envStackId)

        val configFeatureImportList = filteredVersionFeatureImport(
            featureMap = featureMap,
            protocol = protocol
        )

        // 需要注意到，走变更的特性成功后会写配置基线，此时两个来源均会有特性，需要以最新的变更特性为准
        filteredVersionFeatureImportList = crFeatureImportList + configFeatureImportList.distinctBy { it.id }.filter {
            crFeatureImportList.none { crFeatureImport -> crFeatureImport.resourceObjectFeatureKey == it.resourceObjectFeatureKey }
        }

        logger.info("filteredVersionFeatureImportList: ${JsonUtils.writeValueAsString(filteredVersionFeatureImportList)}, crFeatureImportList: ${JsonUtils.writeValueAsString(crFeatureImportList)}, configFeatureImportList: ${JsonUtils.writeValueAsString(configFeatureImportList)}")

        return assembleSpecByFeatureImport(
            resourceObjectFeatureImportList = filteredVersionFeatureImportList,
            protocol = protocol, version = version, systemInputParams = systemInputParams
        )
    }

    /**
     * 抽取不同类型资源的唯一注入模板的生效参数
     *
     * @param workloadMetadataConstraint
     * @param envStackId
     * @return
     */
    fun extractFeatureMap(
        workloadMetadataConstraint: WorkloadMetadataConstraint,
        envStackId: String,
        systemInputParams: Map<String, Any>,
    ): Map<String, Pair<ResourceObjectFeatureImport, MatchScopeDataDO>> {
        val featureMap = mutableMapOf<String, Pair<ResourceObjectFeatureImport, MatchScopeDataDO>>()
        matchScopeService.findMatchScopesByTargetAndExternalForApp(
            targetType = MatchScopeTargetTypeEnum.ResourceObjectFeatureImport.name,
            appName = workloadMetadataConstraint.appName,
            resourceGroupList = listOf(workloadMetadataConstraint.resourceGroup),
            envStackId = envStackId,
            clusterId = workloadMetadataConstraint.clusterId,

        )
            .filter {
                matchScopeService.isMatchScopeRestricted(
                    it.restrictions,
                    workloadMetadataConstraint,
                    envStackId,
                    systemInputParams
                )
            }
            .forEach { matchScopeData ->
                computeFeatureImportMatchScopePriority(
                    featureMap,
                    matchScopeData,
                    workloadMetadataConstraint,
                    envStackId
                )
            }
        return featureMap
    }

    fun extractVersionTraitMap(
        appName: String,
        envStackId: String
    ): Map<String, Pair<ResourceObjectFeatureImport, MatchScopeDataDO>> {
        val featureMap = mutableMapOf<String, Pair<ResourceObjectFeatureImport, MatchScopeDataDO>>()
        matchScopeService.findMatchScopesForVersionOut(
            appName,
            envStackId,
        ).forEach { matchScopeData ->
                computeVersionFeatureImportPriority(
                    featureMap,
                    matchScopeData
                )
            }
        return featureMap
    }

    fun getFeatureImportByAppName(
        req: ResourceObjectFeatureImportGetByAppNameReqDto,
    ): List<ResourceObjectGetFeatureImportDO> {
        val appName = req.appName
        val effectiveStageEnum = req.traitEffectiveStageFilter
        val list = mutableListOf<ResourceObjectGetFeatureImportDO>()
        val groups =
            if (commonProperties.firstOrNull(CommonProperties.LOCAL_APP_2_GROUP) == "true")
                getGroupNameByAppName(appName)
            else
                getGroupNameByAppNameFromSkyline(appName)
        matchScopeService.findMatchScopesByAppNameAndResourceGroupsForResourceObjectFeatureImport(
            appName,
            groups
        ).forEach { matchScopeData ->
            resourceObjectFeatureImportRepo.findById(matchScopeData.targetId!!)?.let { resourceObjectFeatureImport ->
                if (resourceObjectFeatureImport.status != ResourceObjectFeatureImportStatusEnum.ENABLED.name) {
                    return@forEach
                }
                val feature = resourceObjectFeatureRepo.findByResourceObjectFeatureKeyAndVersion(
                    resourceObjectFeatureImport.resourceObjectFeatureKey,
                    resourceObjectFeatureImport.version
                )
                if (
                    effectiveStageEnum.name != feature!!.effectiveStage || ResourceObjectFeatureTypeEnum.INPUT_BLOCK.name == feature.type
                ) {
                    return@forEach
                }
                if (
                    feature.jsonSchema == null && !req.includingSystem
                ) {
                    return@forEach
                }
                list.add(
                    ResourceObjectGetFeatureImportDO(
                        id = resourceObjectFeatureImport.id,
                        resourceObjectFeatureKey = resourceObjectFeatureImport.resourceObjectFeatureKey,
                        status = resourceObjectFeatureImport.status,
                        formData = resourceObjectFeatureImport.paramMap?.let {
                            YamlUtils.load(it)
                        },
                        creator = resourceObjectFeatureImport.creator,
                        modifier = resourceObjectFeatureImport.modifier,
                        gmtCreate = resourceObjectFeatureImport.gmtCreate,
                        gmtModified = resourceObjectFeatureImport.gmtModified,
                        isDeleted = resourceObjectFeatureImport.isDeleted,
                        matchScope = matchScopeData,
                        version = resourceObjectFeatureImport.version
                    )
                )
            }
        }
        if (req.includingLegacy) {
            addLegacyFeatureImports(appName, list)
        }

        return list
    }

    private fun addLegacyFeatureImports(
        appName: String,
        list: MutableList<ResourceObjectGetFeatureImportDO>
    ) {
        dispatchLabelService.getLabelValue(appName = appName).forEach { dispatchLabelValue ->
            val restriction = Restriction(
                site = dispatchLabelValue.idc?.toNullIfBlank(),
                stage = dispatchLabelValue.env?.toNullIfBlank(),
                unit = dispatchLabelValue.unit?.toNullIfBlank(),
            )
            list.add(
                ResourceObjectGetFeatureImportDO(
                    id = dispatchLabelValue.id,
                    resourceObjectFeatureKey = dispatchLabelValue.labelCode,
                    status = ResourceObjectFeatureImportStatusEnum.ENABLED.name,
                    formData = mapOf(
                        "value" to dispatchLabelValue.labelValue
                    ),
                    creator = dispatchLabelValue.creator,
                    modifier = dispatchLabelValue.modifier,
                    gmtCreate = dispatchLabelValue.gmtCreate,
                    gmtModified = dispatchLabelValue.gmtModified,
                    isDeleted = if (dispatchLabelValue.isDeleted == 0) "N" else "Y",
                    matchScope = if (dispatchLabelValue.groupName?.toNullIfBlank() != null) MatchScopeDataDO(
                        externalId = dispatchLabelValue.groupName!!,
                        externalType = MatchScopeExternalTypeEnum.RESOURCE_GROUP.name,
                        restrictions = listOf(
                            restriction
                        ),
                        creator = dispatchLabelValue.creator,
                        modifier = dispatchLabelValue.modifier,
                    )
                    else MatchScopeDataDO(
                        externalId = dispatchLabelValue.appName,
                        externalType = MatchScopeExternalTypeEnum.APPLICATION.name,
                        restrictions = listOf(
                            restriction
                        ),
                        creator = dispatchLabelValue.creator,
                        modifier = dispatchLabelValue.modifier,
                    ),
                    version = DEFAULT_VERSION
                )
            )
        }
    }

    /**
     * 如果featureSpec资源不存在则添加默认资源
     *
     * @param featureMap
     * @param appName
     * @return
     */
    fun addDefaultFeatureSpec(
        featureMap: Map<String, Pair<ResourceObjectFeatureImport, MatchScopeDataDO>>,
        workloadMetadataConstraint: WorkloadMetadataConstraint,
        protocol: String
    ): Map<String, Pair<ResourceObjectFeatureImport, MatchScopeDataDO>> {
        val finalMap = featureMap.toMutableMap()
        var defaultValue = (finalMap[RESOURCE_SPEC_FEATURE_KEY] ?: Pair(
            ResourceObjectFeatureImport(
                resourceObjectFeatureKey = RESOURCE_SPEC_FEATURE_KEY,
                status = ResourceObjectFeatureImportStatusEnum.ENABLED.name,
                paramMap = YamlUtils.dump(getResourceSpec(
                        appName = workloadMetadataConstraint.appName,
                        stage = workloadMetadataConstraint.stage,
                    ),
                ),
                creator = "admin",
                modifier = "admin"
            ),
            MatchScopeDataDO(
                externalId = ALIBABA_GROUP,
                externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name,
                creator = "admin",
                modifier = "admin"
            )
        )).run {
            // 为了跟旧版本设置资源格式一直，格式化资源规格配置值
            Pair(
                this.first.copy(paramMap = resourceSpecFeatureService.formatResourceSpecUnit(this.first.paramMap!!)),
                this.second
            )
        }
        if (protocol == ResourceObjectProtocolEnum.RollingSet.name) {
            defaultValue = Pair(
                defaultValue.first.copy(paramMap = resourceSpecFeatureService.formatDiskUnitToHumanReadable(defaultValue.first.paramMap!!)),
                defaultValue.second
            )
        }

        val envLabels = (finalMap[ENV_LABELS_FEATURE_KEY] ?: Pair(
            ResourceObjectFeatureImport(
                resourceObjectFeatureKey = ENV_LABELS_FEATURE_KEY,
                status = ResourceObjectFeatureImportStatusEnum.ENABLED.name,
                paramMap = YamlUtils.json2yaml(JsonUtils.writeValueAsString(workloadMetadataConstraint)),
                creator = "admin",
                modifier = "admin"
            ),
            MatchScopeDataDO(
                externalId = ALIBABA_GROUP,
                externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name,
                creator = "admin",
                modifier = "admin"
            )
        ))
        finalMap[RESOURCE_SPEC_FEATURE_KEY] = defaultValue
        finalMap[ENV_LABELS_FEATURE_KEY] = envLabels
        if (gropApi.queryCpuShare(
                workloadMetadataConstraint.appName,
                workloadMetadataConstraint.resourceGroup,
                workloadMetadataConstraint.unit,
                workloadMetadataConstraint.stage,
                workloadMetadataConstraint.site,
                )
        ) {
            val cpuShare = (finalMap[CPUSHARE_FEATURE_KEY] ?: Pair(
                ResourceObjectFeatureImport(
                    resourceObjectFeatureKey = CPUSHARE_FEATURE_KEY,
                    status = ResourceObjectFeatureImportStatusEnum.ENABLED.name,
                    paramMap = YamlUtils.dump(
                        emptyMap()
                    ),
                    creator = "admin",
                    modifier = "admin"
                ),
                MatchScopeDataDO(
                    externalId = ALIBABA_GROUP,
                    externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name,
                    creator = "admin",
                    modifier = "admin"
                )
            ))
            finalMap[CPUSHARE_FEATURE_KEY] = cpuShare

        }


        addSafeOutFeatureToServerless(protocol, workloadMetadataConstraint, featureMap, finalMap)

        return finalMap
    }

    private fun addSafeOutFeatureToServerless(
        protocol: String,
        workloadMetadataConstraint: WorkloadMetadataConstraint,
        featureMap: Map<String, Pair<ResourceObjectFeatureImport, MatchScopeDataDO>>,
        finalMap: MutableMap<String, Pair<ResourceObjectFeatureImport, MatchScopeDataDO>>
    ) {
        if (
            protocol == ResourceObjectProtocolEnum.ServerlessApp.name &&
            skylineApi.getAppGroup(workloadMetadataConstraint.resourceGroup).whetherSafeOutOpen()
        ) {
            val defaultSateOutValue = (featureMap[SAFETY_OUT] ?: Pair(
                ResourceObjectFeatureImport(
                    resourceObjectFeatureKey = SAFETY_OUT,
                    status = ResourceObjectFeatureImportStatusEnum.ENABLED.name,
                    paramMap = "{}",
                    creator = "admin",
                    modifier = "admin"
                ),
                MatchScopeDataDO(
                    externalId = ALIBABA_GROUP,
                    externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name,
                    creator = "admin",
                    modifier = "admin"
                )
            )
                    )
            finalMap[SAFETY_OUT] = defaultSateOutValue
        }
    }

    /**
     * 按照useScope[eg:发布/扩容]对需要生效的ResourceObjectFeatureImport进行过滤
     *
     * @param featureMap
     * @param resourceObjectSceneEnum
     * @param resourceObjectBuildTypeEnum
     * @return
     */
    fun filteredResourceObjectFeatureImportByUseScope(
        featureMap: Map<String, Pair<ResourceObjectFeatureImport, MatchScopeDataDO>>,
        resourceObjectSceneEnum: ResourceObjectSceneEnum,
        resourceObjectBuildTypeEnum: ResourceObjectBuildTypeEnum,
        protocol: String
    ): List<ResourceObjectFeatureImport> {
        return featureMap.keys.run {
            if (this.isEmpty()) emptyList() else resourceObjectFeatureRepo.findByResourceObjectFeatureKeyList(this.toList())
        }.filter { resourceObjectFeature ->
            JsonUtils.getObjectMapper().readValue<List<ResourceObjectFeatureUseScope>>(resourceObjectFeature.useScope)
                .contains(
                    ResourceObjectFeatureUseScope(
                        buildType = resourceObjectBuildTypeEnum,
                        scene = resourceObjectSceneEnum
                    )
                )
        }.filter { resourceObjectFeature ->
            resourceObjectFeature.feasibleProtocols.split(COMMA).contains(protocol)
        }.filter { resourceObjectFeature ->
            StringUtils.equals(resourceObjectFeature.effectiveStage, ResourceObjectFeatureEffectiveStageEnum.AFTER_VERSIONOUT.name)
        }.map {
            featureMap[it.resourceObjectFeatureKey]!!.run {
                this.first
            }
        }
    }

    fun filteredVersionFeatureImport(
        featureMap: Map<String, Pair<ResourceObjectFeatureImport, MatchScopeDataDO>>,
        protocol: String
    ): List<ResourceObjectFeatureImport> {
        return featureMap.keys.run {
            if (this.isEmpty()) emptyList() else resourceObjectFeatureRepo.findByResourceObjectFeatureKeyList(this.toList())
        }.filter { resourceObjectFeature ->
            resourceObjectFeature.feasibleProtocols.split(COMMA).contains(protocol)
        }.map {
            featureMap[it.resourceObjectFeatureKey]!!.run {
                if (this.first.version == it.version) {
                    this.first
                } else null
            }
        }.filterNotNull()
    }

    /**
     * 按照ResourceObjectFeatureImport并找到对应spec注入得到完整CRD patch片段
     *
     * @param resourceObjectFeatureImportList
     * @param protocol
     * @param version
     * @param systemInputParams
     * @return
     */
    fun assembleSpecByFeatureImport(
        resourceObjectFeatureImportList: List<ResourceObjectFeatureImport>,
        protocol: String, version: String?, systemInputParams: Map<String, Any>,
        formatType: ResourceObjectFormatEnum = ResourceObjectFormatEnum.YAML,
        extraTemplateFunc: Map<String, Any>? = null,
        workloadMetadataConstraint: WorkloadMetadataConstraint? = null,
    ): List<FeatureImportPatchDO> {
        val featureImportSpecList = mutableListOf<FeatureImportPatchDO>()
        resourceObjectFeatureImportList.map {
            featureImportHookManager.preProcess(it)
        }.forEach { resourceObjectFeatureImport ->
            resourceObjectFeatureProtocolRepo.findByResourceObjectFeatureKeyAndVersion(
                resourceObjectFeatureImport.resourceObjectFeatureKey,
                resourceObjectFeatureImport.version
            )
                .firstOrNull { featureProtocol ->
                    //获取诉求协议&版本的模版
                    protocol == featureProtocol.protocol && version?.toNullIfBlank()
                        ?.run { version == featureProtocol.version } ?: true
                }?.let { featureProtocol ->
                    featureImportSpecList.add(
                        FeatureImportPatchDO(
                            strategy = featureProtocol.strategy?.toNullIfBlank()?.let {
                                JsonUtils.getObjectMapper()
                                    .readValue<PatchStrategyDefinition>(it)
                            },
                            patch = parseTemplate(
                                featureProtocolLoadService.getFeatureProtocol(
                                    featureProtocol,
                                    workloadMetadataConstraint
                                ),
                                resourceObjectFeatureImport.paramMap,
                                formatType,
                                systemInputParams,
                                extraTemplateFunc
                            ),
                            resourceObjectFeatureKey = resourceObjectFeatureImport.resourceObjectFeatureKey,
                            paramMap = resourceObjectFeatureImport.paramMap,
                            creator = resourceObjectFeatureImport.creator,
                            gmtCreate = resourceObjectFeatureImport.gmtCreate,
                            modifier = resourceObjectFeatureImport.modifier,
                            gmtModified = resourceObjectFeatureImport.gmtModified,
                            version = resourceObjectFeatureImport.version,
                        )
                    )
                } ?: let {
                throw ResourceObjectProtocolNotFoundException("未找到${resourceObjectFeatureImport.resourceObjectFeatureKey}切面:${resourceObjectFeatureImport.version}特性的${protocol}协议模版定义.")
            }
        }
        return featureImportSpecList
    }

    /**
     * 根据ids获取FeatureImportPatch
     *
     * @param ids
     * @return
     */
    fun queryFeatureImportPatchDOByIds(ids: List<Long>): List<ResourceObjectFeatureImport> {
        if (ids.isEmpty()) {
            return emptyList()
        }
        return resourceObjectFeatureImportRepo.findByIdList(idList = ids).filter {
            it.status == ResourceObjectFeatureImportStatusEnum.ENABLED.name
        }
    }

    /**
     * 按照条件查询featureImport
     *
     * @param ids
     * @param paramKeyWords
     * @param status
     * @return
     */
    fun queryFeatureImportPatchDOByConditions(
        ids: List<Long>,
        paramKeyWords: String? = null,
        status: String? = ResourceObjectFeatureImportStatusEnum.ENABLED.name
    ): List<ResourceObjectFeatureImport> {
        if (ids.isEmpty()) {
            return emptyList()
        }
        return resourceObjectFeatureImportRepo.findByConditions(
            idList = ids,
            paramKeyWords = paramKeyWords,
            status = status
        )
    }

    @Transactional
    fun createFeature(resourceObjectFeatureCreateReqDto: ResourceObjectFeatureCreateReqDto): Long {
        val resourceObjectFeatureKey = resourceObjectFeatureCreateReqDto.resourceObjectFeatureKey?.apply {
            resourceObjectFeatureRepo.findByResourceObjectFeatureKeyAndVersion(
                this,
                resourceObjectFeatureCreateReqDto.version,
            )?.let {
                throw ResourceObjectException("资源对象特性代码:${resourceObjectFeatureCreateReqDto.resourceObjectFeatureKey}已存在.")
            }
        } ?: KeyGenerator.generateAlphanumericKey(RESOURCE_OBJECT_FEATURE_KEY_LENGTH)
        val resourceObjectFeature = ResourceObjectFeature(
            resourceObjectFeatureKey = resourceObjectFeatureKey,
            title = resourceObjectFeatureCreateReqDto.title,
            creator = resourceObjectFeatureCreateReqDto.creator,
            modifier = resourceObjectFeatureCreateReqDto.creator,
            useScope = JsonUtils.writeValueAsString(resourceObjectFeatureCreateReqDto.useScope),
            feasibleProtocols = resourceObjectFeatureCreateReqDto.feasibleProtocols,
            type = resourceObjectFeatureCreateReqDto.type.name,
            effectiveStage = resourceObjectFeatureCreateReqDto.effectiveStage.name,
            jsonSchema = resourceObjectFeatureCreateReqDto.jsonSchema,
            displayTheme = resourceObjectFeatureCreateReqDto.displayTheme.name,
            version = resourceObjectFeatureCreateReqDto.version,
            submitters = resourceObjectFeatureCreateReqDto.submitters,
        )
        resourceObjectFeatureRepo.insert(resourceObjectFeature)
        resourceObjectFeatureCreateReqDto.matchScopeData?.let { matchScopeData ->
            matchScopeService.createMatchScopeIgnoreWhileExist(
                MatchScopeDataDO(
                    targetId = resourceObjectFeature.id,
                    targetType = MatchScopeTargetTypeEnum.ResourceObjectFeature.name,
                    externalType = matchScopeData.externalType,
                    externalId = matchScopeData.externalId,
                    exclusions = matchScopeData.exclusions,
                    restrictions = matchScopeData.restrictions,
                    creator = resourceObjectFeatureCreateReqDto.creator,
                    modifier = resourceObjectFeatureCreateReqDto.creator
                )
            )
        }
        return resourceObjectFeature.id!!
    }

    @Transactional
    fun updateFeature(resourceObjectFeatureUpdateReqDto: ResourceObjectFeatureUpdateReqDto) {
        val feature = run {
            if (resourceObjectFeatureUpdateReqDto.id != null) {
                resourceObjectFeatureRepo.findById(resourceObjectFeatureUpdateReqDto.id)
            } else if (resourceObjectFeatureUpdateReqDto.resourceObjectFeatureKey != null) {
                resourceObjectFeatureRepo.findByResourceObjectFeatureKeyListAndTypeAndEffectiveStage(
                    resourceObjectFeatureKeyListFilter = listOf(resourceObjectFeatureUpdateReqDto.resourceObjectFeatureKey),
                    version = resourceObjectFeatureUpdateReqDto.version,
                ).apply {
                    if (this.size > 1) {
                        throw ResourceObjectException("资源对象特性Key:${resourceObjectFeatureUpdateReqDto.resourceObjectFeatureKey}存在多个版本,请指定版本号.")
                    }
                }.firstOrNull()
            } else {
                throw ResourceObjectException("更新资源对象需提供资源对象特性主键ID或资源对象特性Key")
            }
        }
        feature?.let {
            if (it.isDeleted == IS_DELETED) {
                throw ResourceObjectException("资源对象ID:${resourceObjectFeatureUpdateReqDto.id}已删除,操作人:${it.modifier}.")
            }
        } ?: let {
            throw ResourceObjectException("资源对象ID:${resourceObjectFeatureUpdateReqDto.id}不存在，更新失败.")
        }
        resourceObjectFeatureRepo.updateById(
            feature.id!!,
            resourceObjectFeatureUpdateReqDto.title,
            if (resourceObjectFeatureUpdateReqDto.useScope.isEmpty()) feature.useScope else JsonUtils.writeValueAsString(resourceObjectFeatureUpdateReqDto.useScope),
            resourceObjectFeatureUpdateReqDto.modifier,
            resourceObjectFeatureUpdateReqDto.feasibleProtocols ?: feature.feasibleProtocols,
            resourceObjectFeatureUpdateReqDto.jsonSchema ?: feature.jsonSchema,
            resourceObjectFeatureUpdateReqDto.type?.name ?: feature.type,
            resourceObjectFeatureUpdateReqDto.effectiveStage?.name ?: feature.effectiveStage,
            resourceObjectFeatureUpdateReqDto.displayTheme?.name ?: feature.displayTheme,
            resourceObjectFeatureUpdateReqDto.version ?: feature.version,
            resourceObjectFeatureUpdateReqDto.submitters ?: feature.submitters,

        )
        matchScopeService.deleteMatchScopeByTarget(
            feature.id!!,
            MatchScopeTargetTypeEnum.ResourceObjectFeature.name,
            resourceObjectFeatureUpdateReqDto.modifier
        )
        resourceObjectFeatureUpdateReqDto.matchScopeData?.let { matchScopeData ->
            matchScopeService.createMatchScopeIgnoreWhileExist(
                MatchScopeDataDO(
                    targetId = resourceObjectFeatureUpdateReqDto.id,
                    targetType = MatchScopeTargetTypeEnum.ResourceObjectFeature.name,
                    externalType = matchScopeData.externalType,
                    externalId = matchScopeData.externalId,
                    exclusions = matchScopeData.exclusions,
                    restrictions = matchScopeData.restrictions,
                    creator = resourceObjectFeatureUpdateReqDto.modifier,
                    modifier = resourceObjectFeatureUpdateReqDto.modifier
                )
            )
        }
    }

    fun getFeature(resourceObjectFeatureGetReqDto: ResourceObjectFeatureGetReqDto): ResourceObjectGetFeatureListDO {
        val featureDOList = mutableListOf<ResourceObjectFeatureFrontDO>()
        val featureList = resourceObjectFeatureRepo.findByResourceObjectFeatureKeyListAndTypeAndEffectiveStage(
            resourceObjectFeatureTypeFilter = resourceObjectFeatureGetReqDto.traitTypeFilter?.name,
            resourceObjectFeatureKeyListFilter = resourceObjectFeatureGetReqDto.traitListFilter,
            resourceObjectFeatureEffectiveStageListFilter = resourceObjectFeatureGetReqDto.traitEffectiveStageListFilter?.map {
                it.name
            },
            submitters = resourceObjectFeatureGetReqDto.submitters,
            userOriented = true,
        )
        val appName = resourceObjectFeatureGetReqDto.matchScopes?.firstOrNull {
            it.externalType == MatchScopeExternalTypeEnum.APPLICATION.name
        }?.externalId
        val appInfo = appName?.run {
            appCenterApi.getAppInfoByName(this).copy(tags = appCenterApi.getAppInfoByNameV2(this).tags)
        }
        featureList.filter { resourceObjectFeature ->
            resourceObjectFeatureGetReqDto.feasibleProtocolFilter?.run {
                resourceObjectFeature.feasibleProtocols.split(COMMA).contains(this)
            } ?: true
        }.filter { resourceObjectFeature ->

            filterByAppInfo(resourceObjectFeature, appInfo)

        }.filter { resourceObjectFeature ->

            filterByType(resourceObjectFeature, resourceObjectFeatureGetReqDto.type)

        }.forEach {
            resourceObjectFeature -> resourceObjectFeature.let {
                featureDOList.add(
                    ResourceObjectFeatureFrontDO(
                        id = it.id!!,
                        resourceObjectFeatureKey = it.resourceObjectFeatureKey,
                        title = it.title,
                        useScope = JsonUtils.readListValue<ResourceObjectFeatureUseScope>(it.useScope, JsonUtils.objectTypeReference()),
                        creator = it.creator,
                        modifier = it.modifier,
                        gmtCreate = it.gmtCreate,
                        gmtModified = it.gmtModified,
                        isDeleted = it.isDeleted,
                        feasibleProtocols = it.feasibleProtocols,
                        type = if (resourceObjectFeature.jsonSchema != null) ResourceObjectFeatureTypeEnum.EDITABLE.name else it.type,
                        effectiveStage = it.effectiveStage,
                        jsonSchema = it.jsonSchema,
                        displayTheme = it.displayTheme,
                        version = it.version,
                        submitters = it.submitters,
                    )
                )

             }
        }
        if (resourceObjectFeatureGetReqDto.traitEffectiveStageListFilter?.contains(ResourceObjectFeatureEffectiveStageEnum.AFTER_VERSIONOUT) ?: false && resourceObjectFeatureGetReqDto.type != ResourceObjectFeatureTypeEnum.EDITABLE) {
            dispatchLabelService.getAllDispatchLabel().forEach { oldFeature ->
                oldFeature.let {
                    featureDOList.add(
                        ResourceObjectFeatureFrontDO(
                            id = it.id!!,
                            resourceObjectFeatureKey = it.code!!,
                            title = it.cnName!!,
                            useScope = emptyList(),
                            creator = it.creator!!,
                            modifier = it.modifier,
                            gmtCreate = it.gmtCreate,
                            gmtModified = it.gmtModified,
                            isDeleted = if (it.isDeleted == 0) "N" else "Y",
                            feasibleProtocols = ResourceObjectProtocolEnum.StatefulSet.name,
                            type = ResourceObjectFeatureTypeEnum.INPUT.name,
                            effectiveStage = ResourceObjectFeatureEffectiveStageEnum.AFTER_VERSIONOUT.name,
                            jsonSchema = DEFAULT_HCRM_TRAIT_JSONSCHEMA,
                            displayTheme = ResourceObjectFeatureDisplayThemeEnum.TREE.name,
                            version = DEFAULT_VERSION,
                            submitters = it.type!!,
                        )
                    )

                }
            }

        }
        return ResourceObjectGetFeatureListDO(
            traitList = featureDOList
        )

    }

    fun getFeatureByKeyAndVersion(req: ResourceObjectFeatureGetByKeyAndVersionReqDto): ResourceObjectGetFeatureListDO {
        val featureList = resourceObjectFeatureRepo.findByResourceObjectFeatureKeyListAndTypeAndEffectiveStage(
            resourceObjectFeatureKeyListFilter = listOf(req.traitKey),
            version = req.version,
        )

        val featureDOList = featureList.map { it ->
            ResourceObjectFeatureFrontDO(
                id = it.id!!,
                resourceObjectFeatureKey = it.resourceObjectFeatureKey,
                title = it.title,
                useScope = JsonUtils.readListValue<ResourceObjectFeatureUseScope>(
                    it.useScope,
                    JsonUtils.objectTypeReference()
                ),
                creator = it.creator,
                modifier = it.modifier,
                gmtCreate = it.gmtCreate,
                gmtModified = it.gmtModified,
                isDeleted = it.isDeleted,
                feasibleProtocols = it.feasibleProtocols,
                type = it.type,
                effectiveStage = it.effectiveStage,
                jsonSchema = it.jsonSchema,
                displayTheme = it.displayTheme,
                version = it.version,
                submitters = it.submitters,
            )


        }
        return ResourceObjectGetFeatureListDO(
            traitList = featureDOList
        )

    }

    fun filterByAppInfo(
        resourceObjectFeature: ResourceObjectFeature,
        appInfo: AppInfo?,
    ): Boolean {
        if (appInfo == null) return true
        val matchScopes = matchScopeService.findFeatureMatchScopeByResourceObjectFeatureKey(
            resourceObjectFeature,
            appInfo
        )
        if (matchScopes.isEmpty()) return true
        return matchScopes.any {
            when (it.externalType) {
                MatchScopeExternalTypeEnum.APPLICATION.name -> {
                    return@any it.externalId == appInfo.name
                }

                MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name -> {
                    return@any it.restrictions?.any {
                        matchScopeService.isMatchRestrictionExtendedLabels(
                            it,
                            mapOf("appInfo" to appInfo),
                        )
                    } ?: true
                }

                else -> {
                    return@any true
                }
            }
        }
    }

    fun filterByType(
        resourceObjectFeature: ResourceObjectFeature,
        type: ResourceObjectFeatureTypeEnum,
    ): Boolean {
        if (type == ResourceObjectFeatureTypeEnum.ALL) return true
        if (type == ResourceObjectFeatureTypeEnum.EDITABLE) {
            return resourceObjectFeature.jsonSchema != null
        }
        return true
    }

    @Transactional
    fun initLightContainerForNewApp(appName: String) {
        val paramMap = commonProperties.firstOrNull(CommonProperties.LIGHTWEIGHT_IMAGE)?.let {
            mapOf("initContainerImage" to it)
        } ?: emptyMap()

        createFeatureImport(
            ResourceObjectFeatureImportCreateReqDto(
                paramMap = paramMap,
                creator = "aquaman",
                resourceObjectFeatureKey = LIGHT_CONTAINER_FEATURE_KEY,
                matchScopeDataList = listOf(
                    MatchScopeDataReqDto(
                        externalId = appName,
                        externalType = MatchScopeExternalTypeEnum.APPLICATION.name
                    )
                )
            )
        )
    }

    @Transactional
    fun createFeatureImport(createReqDto: ResourceObjectFeatureImportCreateReqDto): Long {
        val resourceObjectFeature =
            resourceObjectFeatureRepo.findByResourceObjectFeatureKeyAndVersion(
                createReqDto.resourceObjectFeatureKey,
                createReqDto.version
            ) ?: let {
                throw ResourceObjectException("资源对象特性KEY:${createReqDto.resourceObjectFeatureKey},version:${createReqDto.version}不存在.")
            }
        checkFeatureImportMatchScope(createReqDto.matchScopeDataList, createReqDto.resourceObjectFeatureKey)
        checkRepetitiveFeatureImportForSameScope(createReqDto.matchScopeDataList, createReqDto.resourceObjectFeatureKey)
        val resourceSpecYaml = YamlUtils.dump(createReqDto.paramMap)
        val resourceObjectFeatureImport = ResourceObjectFeatureImport(
            id = null,
            resourceObjectFeatureKey = resourceObjectFeature.resourceObjectFeatureKey,
            status = ResourceObjectFeatureImportStatusEnum.ENABLED.name,
            paramMap = resourceSpecYaml,
            creator = createReqDto.creator,
            modifier = createReqDto.creator,
            version = createReqDto.version,
        )
        resourceObjectFeatureImportRepo.insert(resourceObjectFeatureImport)
        createReqDto.matchScopeDataList.forEach { matchScopeData ->
            matchScopeService.createMatchScopeIgnoreWhileExist(
                MatchScopeDataDO(
                    targetId = resourceObjectFeatureImport.id,
                    targetType = MatchScopeTargetTypeEnum.ResourceObjectFeatureImport.name,
                    externalType = matchScopeData.externalType,
                    externalId = matchScopeData.externalId,
                    exclusions = matchScopeData.exclusions,
                    restrictions = matchScopeData.restrictions,
                    creator = createReqDto.creator,
                    modifier = createReqDto.creator
                )
            )
        }
        return resourceObjectFeatureImport.id!!
    }

    @Transactional
    fun createOrUpdateFeatureImport(req: ResourceObjectFeatureImportCreateOrUpdateReqDto) {
        req.createOrUpdateTraitList.forEach { deletingTrait ->
            deleteFeatureImportByMatchScope(
                ResourceObjectFeatureImportDeleteByMatchScopeReqDto(
                    resourceObjectFeatureKey = deletingTrait.traitKey,
                    modifier = req.modifier,
                    matchScopes = req.matchScopes
                )
            )
        }
        req.createOrUpdateTraitList.forEach { addingTrait ->
            createFeatureImport(
                ResourceObjectFeatureImportCreateReqDto(
                    resourceObjectFeatureKey = addingTrait.traitKey,
                    paramMap = addingTrait.formData,
                    status = addingTrait.status,
                    creator = req.modifier,
                    matchScopeDataList = req.matchScopes,
                    version = addingTrait.version,
                )
            )
        }

    }

    @Transactional
    fun updateFeatureImport(updateReqDto: ResourceObjectFeatureImportUpdateReqDto) {
        val resourceObjectFeatureImport = resourceObjectFeatureImportRepo.findById(updateReqDto.id)?.apply {
            if (this.isDeleted == IS_DELETED) {
                throw ResourceObjectException("资源特性导入ID:${updateReqDto.id}已删除,操作人:${this.modifier}.")
            }
        } ?: let {
            throw ResourceObjectException("未找到预更新的资源特性导入,id:${updateReqDto.id}")
        }
        checkFeatureImportMatchScope(
            updateReqDto.matchScopeDataList,
            resourceObjectFeatureImport.resourceObjectFeatureKey
        )
        matchScopeService.deleteMatchScopeByTarget(
            updateReqDto.id,
            MatchScopeTargetTypeEnum.ResourceObjectFeatureImport.name,
            updateReqDto.modifier
        )
        checkRepetitiveFeatureImportForSameScope(
            updateReqDto.matchScopeDataList,
            resourceObjectFeatureImport.resourceObjectFeatureKey
        )
        val resourceSpecYaml = YamlUtils.dump(updateReqDto.paramMap)
        resourceObjectFeatureImportRepo.updateById(
            updateReqDto.id,
            updateReqDto.modifier,
            resourceSpecYaml,
            updateReqDto.status.name,
            updateReqDto.version ?: resourceObjectFeatureImport.version,
        )
        updateReqDto.matchScopeDataList.forEach { matchScopeData ->
            matchScopeService.createMatchScopeIgnoreWhileExist(
                MatchScopeDataDO(
                    targetId = resourceObjectFeatureImport.id,
                    targetType = MatchScopeTargetTypeEnum.ResourceObjectFeatureImport.name,
                    externalType = matchScopeData.externalType,
                    externalId = matchScopeData.externalId,
                    exclusions = matchScopeData.exclusions,
                    restrictions = matchScopeData.restrictions,
                    creator = updateReqDto.modifier,
                    modifier = updateReqDto.modifier
                )
            )
        }
    }

    @Transactional
    fun modifyTraits(traitModifyReq: TraitModifyReq) {
        groupTraitBySubmiiter(traitModifyReq).forEach {
            modifyTraitsBySubmiiter(it)
        }
    }

    fun modifyTraitsBySubmiiter(traitModifyReqDto: TraitModifyReqDto) {
        if (traitModifyReqDto.createOrUpdateTraitList?.isNotEmpty() ?: false) {
            traitModifyReqDto.createOrUpdateTraitList?.forEach { deletingTrait ->
                deleteFeatureImportByMatchScope(
                    ResourceObjectFeatureImportDeleteByMatchScopeReqDto(
                        resourceObjectFeatureKey = deletingTrait.traitKey,
                        modifier = traitModifyReqDto.modifier,
                        matchScope = traitModifyReqDto.matchScope
                    )
                )
            }
            traitModifyReqDto.createOrUpdateTraitList?.forEach { addingTrait ->
                createFeatureImport(
                    ResourceObjectFeatureImportCreateReqDto(
                        resourceObjectFeatureKey = addingTrait.traitKey,
                        paramMap = addingTrait.formData,
                        status = addingTrait.status,
                        creator = traitModifyReqDto.modifier,
                        matchScopeDataList = listOf(traitModifyReqDto.matchScope),
                        version = addingTrait.version,
                    )
                )
            }
        } else {
            traitModifyReqDto.addingTraitList?.forEach { addingTrait ->
                createFeatureImport(
                    ResourceObjectFeatureImportCreateReqDto(
                        resourceObjectFeatureKey = addingTrait.traitKey,
                        paramMap = addingTrait.formData,
                        status = addingTrait.status,
                        creator = traitModifyReqDto.modifier,
                        matchScopeDataList = listOf(traitModifyReqDto.matchScope),
                        version = addingTrait.version,
                    )
                )
            }
            traitModifyReqDto.updatingTraitList?.forEach { updatingTrait ->
                updateFeatureImportByMatchScope(
                    ResourceObjectFeatureImportUpdateByMatchScopeReqDto(
                        resourceObjectFeatureKey = updatingTrait.traitKey,
                        paramMap = updatingTrait.formData,
                        status = updatingTrait.status,
                        modifier = traitModifyReqDto.modifier,
                        matchScope = traitModifyReqDto.matchScope,
                        version = updatingTrait.version,
                    )
                )
            }
        }
        traitModifyReqDto.deletingTraitList?.forEach { deletingTrait ->
            deleteFeatureImportByMatchScope(
                ResourceObjectFeatureImportDeleteByMatchScopeReqDto(
                    resourceObjectFeatureKey = deletingTrait.traitKey,
                    modifier = traitModifyReqDto.modifier,
                    matchScope = traitModifyReqDto.matchScope
                )
            )
        }
        appendActionLog(traitModifyReqDto)
    }

    fun groupTraitBySubmiiter(traitModifyReq: TraitModifyReq): List<TraitModifyReqDto> {
        val addGroup = traitModifyReq.addingTraitList?.groupBy {
            resourceObjectFeatureRepo.findByResourceObjectFeatureKey(
                it.traitKey,
            ).firstOrNull()?.submitters
        }
        val addUpdateGroup = traitModifyReq.createOrUpdateTraitList?.groupBy {
            resourceObjectFeatureRepo.findByResourceObjectFeatureKey(
                it.traitKey,
            ).firstOrNull()?.submitters
        }
        val updateGroup = traitModifyReq.updatingTraitList?.groupBy {
            resourceObjectFeatureRepo.findByResourceObjectFeatureKey(
                it.traitKey,
            ).firstOrNull()?.submitters
        }
        val delGroup = traitModifyReq.deletingTraitList?.groupBy {
            resourceObjectFeatureRepo.findByResourceObjectFeatureKey(
                it.traitKey,
            ).firstOrNull()?.submitters
        }
        val ret = mutableListOf<TraitModifyReqDto>()
        val submitters =
            listOf(addGroup?.keys, addUpdateGroup?.keys, updateGroup?.keys, delGroup?.keys).flatMap { it.orEmpty() }
                .distinct().filterNotNull()
        submitters.forEach {
            ret.add(
                TraitModifyReqDto(
                    matchScope = traitModifyReq.matchScope,
                    addingTraitList = addGroup?.get(it),
                    createOrUpdateTraitList = addUpdateGroup?.get(it),
                    updatingTraitList = updateGroup?.get(it),
                    deletingTraitList = delGroup?.get(it),
                    submitters = it,
                    modifier = traitModifyReq.modifier,
                )
            )
        }
        return ret
    }

    @Transactional
    fun appendActionLog(traitModifyReqDto: TraitModifyReqDto) {
        val traitKeyList = getTraitKeyList(traitModifyReqDto)
        val traitListSnapShot = getFeatureImportByMatchScope(
            ResourceObjectFeatureImportGetByMatchScopeReqDto(
                keyList = null,
                matchScopes = listOf(traitModifyReqDto.matchScope),
                traitTypeFilter = ResourceObjectFeatureTypeEnum.INPUT,
                traitEffectiveStageListFilter = listOf(
                    ResourceObjectFeatureEffectiveStageEnum.DURING_VERSIONOUT,
                    ResourceObjectFeatureEffectiveStageEnum.DISPLAY
                ),
                submitters = traitModifyReqDto.submitters,
            )
        )
        val traitListSnapShotStr = JsonUtils.writeValueAsString(traitListSnapShot)
        val actionLog = ActionLog(
            id = null,
            actionType = buildActionType(traitModifyReqDto),
            targetType = MatchScopeTargetTypeEnum.ResourceObjectFeatureImport.name,
            targetName = traitKeyList.joinToString(separator = ","),
            params = traitListSnapShotStr,
            operator = traitModifyReqDto.modifier,
            sourceType = traitModifyReqDto.matchScope.externalType,
            submitters = traitModifyReqDto.submitters,
            sourceId = traitModifyReqDto.matchScope.externalId
        )
        actionLogRepo.insert(actionLog)
    }



    @Transactional
    fun updateFeatureImportByMatchScope(req: ResourceObjectFeatureImportUpdateByMatchScopeReqDto) {
        // 通过匹配范围查询特性导入
        val featureImportMatchScope = matchScopeService.findMatchScopeByExternalAndResourceObjectFeatureKey(
            req.matchScope,
            req.resourceObjectFeatureKey
        ) ?: throw ResourceObjectException(
            "资源对象特性导入不存在, externalType:${req.matchScope.externalType}, " +
                    "externalId: ${req.matchScope.externalId}, featureKey: ${req.resourceObjectFeatureKey}"
        )

        // 通过特性导入反查匹配范围，确保特性导入只关联了一条匹配范围记录
        val featureImportId = checkNotNull(featureImportMatchScope.targetId)
        // 通过特性导入反查匹配范围
        val matchScopesByFeatureImportId = matchScopeService.listByTarget(
            featureImportId,
            MatchScopeTargetTypeEnum.ResourceObjectFeatureImport.name
        )
        if (matchScopesByFeatureImportId.size != 1) {
            val matchScopeIds = matchScopesByFeatureImportId.map { it.id }.joinToString()
            throw ResourceObjectException(
                "资源对象特性导入存在多个匹配范围不允许更新, featureImportId:$featureImportId, match scopes:$matchScopeIds"
            )
        }
        val actualMatchScopeId = matchScopesByFeatureImportId[0].id
        check(actualMatchScopeId == featureImportMatchScope.id) {
            "无法更新资源特性导入，关联的匹配范围与预期不符，预期关联 ${featureImportMatchScope.id}, 实际关联 $actualMatchScopeId"
        }

        // 更新特性导入
        val featureImport = resourceObjectFeatureImportRepo.findById(featureImportId)
            ?: throw ResourceObjectException("资源对象特性导入不存在, featureImportId:$featureImportId")
        resourceObjectFeatureImportRepo.updateById(
            featureImportId,
            req.modifier,
            req.paramMap?.run(YamlUtils::dump) ?: featureImport.paramMap ?: "",
            req.status?.name ?: checkNotNull(featureImport.status),
            req.version ?: featureImport.version
        )
    }

    @Transactional
    fun deleteFeatureImportById(id: Long, modifier: String) {
        resourceObjectFeatureImportRepo.findById(id)?.let {
            // 删除特性导入匹配范围
            matchScopeService.deleteMatchScopeByTarget(
                it.id!!,
                MatchScopeTargetTypeEnum.ResourceObjectFeatureImport.name,
                modifier
            )
            // 删除特性导入
            resourceObjectFeatureImportRepo.deleteById(it.id!!)
        } ?: ResourceObjectException("资源特性导入ID:${id}不存在,操作人:${modifier}.")
    }

    /**
     * 针对指定的匹配范围移除资源特性导入
     */
    @Transactional
    fun deleteFeatureImportByMatchScope(req: ResourceObjectFeatureImportDeleteByMatchScopeReqDto) {
        req.copy(
            matchScopes = req.matchScopes.ifEmpty { listOf(req.matchScope!!) }
        ).matchScopes.forEach {
            // 通过匹配范围查询特性导入
            val featureImportMatchScope = matchScopeService.findMatchScopeByExternalAndResourceObjectFeatureKey(
                it,
                req.resourceObjectFeatureKey
            ) ?: return
            modifierValidator(featureImportMatchScope.modifier, req.modifier)

            // 删除 match scope
            matchScopeService.deleteMatchScope(featureImportMatchScope, req.modifier)

            val featureImportId = checkNotNull(featureImportMatchScope.targetId)
            // 通过特性导入反查匹配范围
            val matchScopesByFeatureImportId = matchScopeService.listByTarget(
                featureImportId,
                MatchScopeTargetTypeEnum.ResourceObjectFeatureImport.name
            )
            // 如果 feature import 没有删除其它 match scope，级联删除 feature import
            if (matchScopesByFeatureImportId.isEmpty()) {
                resourceObjectFeatureImportRepo.deleteById(featureImportId)
            } else {
                logger.info("feature import({}) still referenced by match scopes({}), cannot be deleted",
                    featureImportId, matchScopesByFeatureImportId.map { it.id }.joinToString()
                )
            }
        }

    }

    fun modifierValidator(originalModifier: String, newModifier: String) {
        if (originalModifier != newModifier && commonProperties.contains(LOW_RANK_MODIFIER, newModifier)) {
            throw ResourceObjectException("操作人不一致且新操作人权限低,原操作人:$originalModifier,新操作人:$newModifier.")
        }
    }

    @Transactional
    fun batchDeleteFeatureImportByMatchScope(req: BatchResourceObjectFeatureImportDeleteByMatchScopeReqDto) {
        req.deletingTraitList.forEach {
            deleteFeatureImportByMatchScope(
                ResourceObjectFeatureImportDeleteByMatchScopeReqDto(
                    resourceObjectFeatureKey = it.traitKey,
                    modifier = req.modifier,
                    matchScopes = req.matchScopes
                )
            )
        }

    }

    fun getActionLogs(req: ActionLogsReqDto): ResourceObjectGetActionLogDO {
        val actionLogs = actionLogRepo.listByMatchScopeWithSubmitters(req.externalType, req.externalId, req.submitters)
        return ResourceObjectGetActionLogDO(
            snapShotHistory = actionLogs
        )
    }

    fun getFeatureImportByMatchScope(req: ResourceObjectFeatureImportGetByMatchScopeReqDto): ResourceObjectGetFeatureImportListDO {
        val ResourceObjectGetFeatureImportDOList = mutableListOf<ResourceObjectGetFeatureImportDO>()
        val featureMap = mutableMapOf<String, Pair<ResourceObjectFeatureImport, MatchScopeDataDO>>()
        val featureList =
            resourceObjectFeatureRepo.findByResourceObjectFeatureKeyListAndTypeAndEffectiveStage(
                req.keyList,
                req.traitEffectiveStageListFilter?.map { it.name }, req.traitTypeFilter?.name,
                userOriented = true,
                submitters = req.submitters
            )
        featureList.map { it -> it.resourceObjectFeatureKey }.distinct().forEach { feature ->
            req.matchScopes.forEach innerForEach@{ matchScope ->
                val featureImportMatchScope = matchScopeService.findMatchScopeByExternalAndResourceObjectFeatureKey(
                    matchScope,
                    feature
                ) ?: return@innerForEach
                computeVersionFeatureImportPriority(
                    featureMap,
                    featureImportMatchScope
                )
            }

        }
        featureMap.values.forEach { (featureImport, matchScopeData) ->
            ResourceObjectGetFeatureImportDOList.add(
                ResourceObjectGetFeatureImportDO(
                    id = featureImport.id,
                    resourceObjectFeatureKey = featureImport.resourceObjectFeatureKey,
                    status = featureImport.status,
                    formData = featureImport.paramMap?.let {
                        YamlUtils.load(it)
                    },
                    creator = featureImport.creator,
                    modifier = featureImport.modifier,
                    gmtCreate = featureImport.gmtCreate,
                    gmtModified = featureImport.gmtModified,
                    isDeleted = featureImport.isDeleted,
                    externalType = matchScopeData.externalType,
                    externalId = matchScopeData.externalId,
                    version = featureImport.version
                )
            )
        }

        return ResourceObjectGetFeatureImportListDO(
            traitDetailList = ResourceObjectGetFeatureImportDOList
        )

    }

    fun getGroupNameByAppName(appName: String): List<String> {
        return kvMapService.getValuesAsListByTypeAndKey(
            KvMapTypeEnum.APPNAME_2_GROUPNAME.name,
            appName
        )
    }

    fun getGroupNameByAppNameFromSkyline(appName: String): List<String> {
        return skylineApi.getAppGroupsByAppName(appName).map {
            it -> it.name
        }
    }

    fun getFeatureImportByMatchScopeAndKey(req: ResourceObjectFeatureImportGetByMatchScopeAndKeyReqDto): List<ResourceObjectGetFeatureImportDO> {
        val ResourceObjectGetFeatureImportDOList = mutableListOf<ResourceObjectGetFeatureImportDO>()
        matchScopeService.listByTargetTypeAndExternal(
            MatchScopeTargetTypeEnum.ResourceObjectFeatureImport.name,
            req.matchScope.externalId,
            req.matchScope.externalType,
        ).filter {
            it.restrictions.orEmpty().equalsIgnoreOrder(req.matchScope.restrictions.orEmpty())
        }.forEach {
            val featureImport =
                resourceObjectFeatureImportRepo.findByIdList(listOf(it.targetId!!)).firstOrNull() ?: return@forEach
            if (featureImport.resourceObjectFeatureKey != req.resourceObjectFeatureKey) {
                return@forEach
            }
            ResourceObjectGetFeatureImportDOList.add(
                ResourceObjectGetFeatureImportDO(
                    id = featureImport.id,
                    resourceObjectFeatureKey = featureImport.resourceObjectFeatureKey,
                    status = featureImport.status,
                    formData = featureImport.paramMap?.let {
                        YamlUtils.load(it)
                    },
                    creator = featureImport.creator,
                    modifier = featureImport.modifier,
                    gmtCreate = featureImport.gmtCreate,
                    gmtModified = featureImport.gmtModified,
                    isDeleted = featureImport.isDeleted,
                    externalType = req.matchScope.externalType,
                    externalId = req.matchScope.externalId,
                    version = featureImport.version,
                )
            )
        }
        return ResourceObjectGetFeatureImportDOList

    }

    /**
     * 根据特性key，分页获取组合后的特性
     * nextToken指定为targetId
     * 由于tarGetId非唯一，因此对于nextToken需要通过>=进行约束。
     * asi需要在查询后针对已经处理过的feature进行过滤
     */
    fun pageGetCombinedFeatureImportByKeys(
        req: ResourceObjectFeatureImportAsiAwareReqDto
    ): NextTokenData<ResourceObjectFeatureImportForAsiDO> {

        // 校验查询trait的合法性，合法trait持久化于diamond
        if (req.traitKeyList.isEmpty()) {
            return NextTokenData(data = emptyList(), pageSize = req.pageSize)
        }
        checkAsiTraitKeyPriviledge(req.traitKeyList)

        val traits = resourceObjectFeatureImportRepo.findCombineByKeys(
            keyList = req.traitKeyList,
            status = ResourceObjectFeatureImportStatusEnum.ENABLED.name,
            exTypeList = listOf(
                MatchScopeExternalTypeEnum.RESOURCE_GROUP.name,
                MatchScopeExternalTypeEnum.QUOTA_NAME.name,
                MatchScopeExternalTypeEnum.APPLICATION.name
            ),
            matchScopeIdToken = req.nextToken,
            pageSize = req.pageSize
        )

        // 不满1页说明查完了
        val nextToken = if (traits.size < req.pageSize) {
            null
        } else {
            traits.lastOrNull()?.importId.toString()
        }

        // formData格式化 和 restriction格式化
        val bizData = traits.map { queryDO ->
            val featureImport = ResourceObjectFeatureImport(
                id = queryDO.importId,
                resourceObjectFeatureKey = queryDO.resourceObjectFeatureKey,
                status = ResourceObjectFeatureImportStatusEnum.ENABLED.name,
                paramMap = queryDO.paramMap,
                // 不依赖创建信息，填充默认值
                creator = StringUtils.EMPTY,
                modifier = StringUtils.EMPTY
            )
            val fixedImport = featureImportHookManager.preProcess(featureImport)

            ResourceObjectFeatureImportForAsiDO(
                traitKey = queryDO.resourceObjectFeatureKey,
                externalType = queryDO.externalType,
                externalId = queryDO.externalId,
                targetId = queryDO.importId,
                formData = YamlUtils.load(fixedImport.paramMap!!),
                restrictions = queryDO.restrictions?.toNullIfBlank()?.run {
                    objectMapper.readValue<MutableList<Restriction>>(this)
                },
            )
        }

        return NextTokenData(nextToken = nextToken, data = bizData, pageSize = req.pageSize)
    }

    /**
     * 校验asi查询的特性key是否具备权限
     */
    private fun checkAsiTraitKeyPriviledge(keys: List<String>) {
        val legalKeys = commonProperties.get(CommonProperties.PAGE_QUERY_ASI_AWARE_TRAIT_KEY)
        if (legalKeys.isEmpty()) {
            throw ResourceObjectException("指定特性Key无查询权限!")
        }

        if (!keys.all { it in legalKeys }) {
            throw ResourceObjectException("指定特性Key无查询权限!")
        }
    }

    @Transactional
    fun createFeatureProtocol(resourceObjectFeatureProtocolCreateReqDto: ResourceObjectFeatureProtocolCreateReqDto): Long {
        resourceObjectFeatureRepo.findByResourceObjectFeatureKeyAndVersion(
            resourceObjectFeatureProtocolCreateReqDto.resourceObjectFeatureKey,
            resourceObjectFeatureProtocolCreateReqDto.version
        )
            ?: let {
                throw ResourceObjectException("资源对象特性:${resourceObjectFeatureProtocolCreateReqDto.resourceObjectFeatureKey}不存在.")
            }
        resourceObjectFeatureProtocolRepo.findByResourceObjectFeatureKeyAndVersion(
            resourceObjectFeatureProtocolCreateReqDto.resourceObjectFeatureKey,
            resourceObjectFeatureProtocolCreateReqDto.version
        )
            .firstOrNull {
                it.protocol == resourceObjectFeatureProtocolCreateReqDto.protocol.name
            }?.let {
                throw ResourceObjectException("资源对象特性:${resourceObjectFeatureProtocolCreateReqDto.resourceObjectFeatureKey}协议[${resourceObjectFeatureProtocolCreateReqDto.protocol.name}]配置已存在.")
            }

        val resourceObjectFeatureProtocol = ResourceObjectFeatureProtocol(
            resourceObjectFeatureKey = resourceObjectFeatureProtocolCreateReqDto.resourceObjectFeatureKey,
            protocol = resourceObjectFeatureProtocolCreateReqDto.protocol.name,
            patch = resourceObjectFeatureProtocolCreateReqDto.patch,
            version = resourceObjectFeatureProtocolCreateReqDto.version,
            creator = resourceObjectFeatureProtocolCreateReqDto.creator,
            modifier = resourceObjectFeatureProtocolCreateReqDto.creator,
            strategy = resourceObjectFeatureProtocolCreateReqDto.strategy?.let {
                JsonUtils.getObjectMapper()
                    .writeValueAsString(it)
            }
        )
        resourceObjectFeatureProtocolRepo.insert(resourceObjectFeatureProtocol)
        return resourceObjectFeatureProtocol.id!!
    }

    /**
     * 创建扩展featureProtocolExt对象 不涉及发布&扩容等场景
     *
     * @param resourceObjectFeatureProtocolExtCreateReqDto
     * @return
     */
    @Transactional
    fun createFeatureProtocolExt(resourceObjectFeatureProtocolExtCreateReqDto: ResourceObjectFeatureProtocolExtCreateReqDto): Long {
        return createFeatureProtocolInner(
            resourceObjectFeatureKey = resourceObjectFeatureProtocolExtCreateReqDto.resourceObjectFeatureKey,
            protocolType = resourceObjectFeatureProtocolExtCreateReqDto.protocol.name,
            patch = resourceObjectFeatureProtocolExtCreateReqDto.patch,
            creator = resourceObjectFeatureProtocolExtCreateReqDto.creator,
            strategy = resourceObjectFeatureProtocolExtCreateReqDto.strategy
        )
    }

    fun createFeatureProtocolInner(
        resourceObjectFeatureKey: String,
        protocolType: String,
        patch: String,
        creator: String,
        strategy: PatchStrategyDefinition? = null
    ): Long {
        resourceObjectFeatureProtocolRepo.findByResourceObjectFeatureKey(resourceObjectFeatureKey).firstOrNull {
            it.protocol == protocolType
        }?.let {
            throw ResourceObjectException("资源对象特性:${resourceObjectFeatureKey}协议[${protocolType}]配置已存在.")
        }
        val resourceObjectFeatureProtocol = ResourceObjectFeatureProtocol(
            resourceObjectFeatureKey = resourceObjectFeatureKey,
            protocol = protocolType,
            patch = patch,
            creator = creator,
            modifier = creator,
            strategy = strategy?.let {
                JsonUtils.getObjectMapper()
                    .writeValueAsString(it)
            }
        )
        resourceObjectFeatureProtocolRepo.insert(resourceObjectFeatureProtocol)
        return resourceObjectFeatureProtocol.id!!
    }


    @Transactional
    fun updateFeatureProtocol(resourceObjectFeatureProtocolUpdateReqDto: ResourceObjectFeatureProtocolUpdateReqDto) {
        val saved = resourceObjectFeatureProtocolRepo.findById(resourceObjectFeatureProtocolUpdateReqDto.id)
        saved?.let {
            if (it.isDeleted == IS_DELETED) {
                throw ResourceObjectException("资源对象特性导入ID:${resourceObjectFeatureProtocolUpdateReqDto.id}已删除,操作人:${it.modifier}.")
            }
        } ?: let {
            throw ResourceObjectException("资源对象特性导入ID:${resourceObjectFeatureProtocolUpdateReqDto.id}不存在，更新失败.")
        }
        resourceObjectFeatureProtocolRepo.updateById(
            resourceObjectFeatureProtocolUpdateReqDto.id,
            resourceObjectFeatureProtocolUpdateReqDto.patch,
            resourceObjectFeatureProtocolUpdateReqDto.modifier,
            resourceObjectFeatureProtocolUpdateReqDto.strategy?.let {
                JsonUtils.writeValueAsString(it as Any)
            },
        )
    }

    fun getFeatureImportByMatchScopeAndFeatureKey(
        matchScopeExternalType: String,
        matchScopeExternalId: String,
        resourceObjectFeatureKey: String
    ): ResourceObjectFeatureImportDO? {
        return resourceObjectFeatureImportRepo.findByMatchScopeAndFeatureKey(
            matchScopeExternalType,
            matchScopeExternalId,
            resourceObjectFeatureKey
        )?.run {
            ResourceObjectFeatureImportDO(
                this.id!!,
                this.resourceObjectFeatureKey,
                this.status,
                this.paramMap?.run { YamlUtils.load(this) } ?: kotlin.run { emptyMap() },
                this.creator,
                this.modifier,
                this.gmtCreate,
                this.gmtModified,
                this.isDeleted,
                emptyList()
            )
        }
    }

    /**
     * 计算特性导入匹配范围优先级
     * 优先范围就小原则
     * @param featureMap KEY：特性导入KEY，VALUE：特性导入&匹配范围
     * @param matchScopeData 匹配范围
     */
    private fun computeFeatureImportMatchScopePriority(
        featureMap: MutableMap<String, Pair<ResourceObjectFeatureImport, MatchScopeDataDO>>,
        matchScopeData: MatchScopeDataDO,
        workloadMetadataConstraint: WorkloadMetadataConstraint,
        envStackId: String
    ) {
        resourceObjectFeatureImportRepo.findById(matchScopeData.targetId!!)?.let { resourceObjectFeatureImport ->
            if (resourceObjectFeatureImport.status != ResourceObjectFeatureImportStatusEnum.ENABLED.name) {
                return
            }
            val featureImportAndMatchScopePair = featureMap[resourceObjectFeatureImport.resourceObjectFeatureKey]
            if (featureImportAndMatchScopePair == null) {
                featureMap[resourceObjectFeatureImport.resourceObjectFeatureKey] = Pair(
                    resourceObjectFeatureImport,
                    matchScopeData
                )
                return
            }
            // 按优先级比较过滤
            val compareScopePriorityResult = matchScopeService.compareScopePriority(
                matchScopeData.externalType,
                matchScopeData.externalId,
                featureImportAndMatchScopePair.second.externalType,
                featureImportAndMatchScopePair.second.externalId
            )
            when {
                compareScopePriorityResult > 0 -> {
                    featureMap[resourceObjectFeatureImport.resourceObjectFeatureKey] = Pair(
                        resourceObjectFeatureImport,
                        matchScopeData
                    )
                }


                compareScopePriorityResult == 0 -> {
                    //如果匹配范围重复，继续按照限定条件
                    val compareScopeRestrictionResult = matchScopeService.compareRestrictionPriority(
                        matchScopeData.restrictions,
                        featureImportAndMatchScopePair.second.restrictions, workloadMetadataConstraint, envStackId
                    )
                    when {
                        compareScopeRestrictionResult > 0 -> {
                            featureMap[resourceObjectFeatureImport.resourceObjectFeatureKey] = Pair(
                                resourceObjectFeatureImport,
                                matchScopeData
                            )
                        }
                        //如果匹配范围&限定条件都重复，抛异常
                        //TODO 最好特性导入新增/更新入口加重复检测
                        compareScopeRestrictionResult == 0 ->
                            throw MatchScopeDataException(
                                "重复的特性【key:${resourceObjectFeatureImport.resourceObjectFeatureKey}】授权," +
                                        "授权范围【externalType:${matchScopeData.externalType}, externalId:${matchScopeData.externalId}】," +
                                        "限定条件:${matchScopeData.restrictions}"
                            )
                    }
                }
            }
        }
    }

    private fun computeVersionFeatureImportPriority(
        featureMap: MutableMap<String, Pair<ResourceObjectFeatureImport, MatchScopeDataDO>>,
        matchScopeData: MatchScopeDataDO
    ) {
        resourceObjectFeatureImportRepo.findById(matchScopeData.targetId!!)?.let { resourceObjectFeatureImport ->
            if (resourceObjectFeatureImport.status != ResourceObjectFeatureImportStatusEnum.ENABLED.name) {
                return
            }
            val feature = resourceObjectFeatureRepo.findByResourceObjectFeatureKeyAndVersion(
                resourceObjectFeatureImport.resourceObjectFeatureKey,
                resourceObjectFeatureImport.version
            )
            if (
                ResourceObjectFeatureEffectiveStageEnum.AFTER_VERSIONOUT.name == feature!!.effectiveStage || ResourceObjectFeatureTypeEnum.INPUT_BLOCK.name == feature.type
            ) {
                return
            }
            val featureImportAndMatchScopePair = featureMap[resourceObjectFeatureImport.resourceObjectFeatureKey]
            if (featureImportAndMatchScopePair == null) {
                featureMap[resourceObjectFeatureImport.resourceObjectFeatureKey] = Pair(
                    resourceObjectFeatureImport,
                    matchScopeData
                )
                return
            }
            // 按优先级比较过滤
            val compareScopePriorityResult = matchScopeService.compareScopePriority(
                matchScopeData.externalType,
                matchScopeData.externalId,
                featureImportAndMatchScopePair.second.externalType,
                featureImportAndMatchScopePair.second.externalId
            )
            when {
                compareScopePriorityResult > 0 -> {
                    featureMap[resourceObjectFeatureImport.resourceObjectFeatureKey] = Pair(
                        resourceObjectFeatureImport,
                        matchScopeData
                    )
                }


                compareScopePriorityResult == 0 -> {
                    throw MatchScopeDataException(
                                "重复的特性【key:${resourceObjectFeatureImport.resourceObjectFeatureKey}】授权," +
                                        "授权范围【externalType:${matchScopeData.externalType}, externalId:${matchScopeData.externalId}】," +
                                        "限定条件:${matchScopeData.restrictions}"
                            )
                    }
                }
            }

    }

    private fun checkFeatureImportMatchScope(
        matchScopeDataList: List<MatchScopeDataReqDto>,
        resourceObjectFeatureKey: String
    ) {
        matchScopeDataList.forEach { matchScopeData ->
            //校验特性是否有限定绑定范围
            RESOURCE_OBJECT_FEATURE_SCOPE[resourceObjectFeatureKey]?.let {
                if (!it.contains(matchScopeData.externalType)) {
                    throw ResourceObjectException("资源对象特性[${resourceObjectFeatureKey}]限定绑定范围为[${it}].")
                }
            }
        }
    }

    fun parseTemplate(
        template: String,
        userParamsMapStr: String?,
        userParamsMapFormatEnum: ResourceObjectFormatEnum,
        systemParams: Map<String, Any>,
        extraTemplateFunc: Map<String, Any>? = null
    ): String {
        var userParamMap = userParamsMapStr?.run {
            when (userParamsMapFormatEnum) {
                ResourceObjectFormatEnum.YAML -> YamlUtils.load(userParamsMapStr)
                ResourceObjectFormatEnum.JSON -> JsonUtils.readValue(userParamsMapStr, Map::class.java)
            }
        }
        // 所有的额外的文本处理函数在userParamMap的根路径上
        userParamMap = if (extraTemplateFunc != null) {
            userParamMap?.toMutableMap()?.apply {
                this.putAll(extraTemplateFunc)
            }
        } else userParamMap
        return mapOf(
            INPUT_USER_PARAMS_KEY to userParamMap as Any,
            INPUT_SYSTEM_PARAMS_KEY to systemParams as Any
        ).run {
            FreemarkerUtils.parseTemplate(template, this)
        }
    }

    /**
     * 获取资源规格
     */
    private fun getResourceSpec(appName: String, stage: String): Map<String, Any> {
        val resourceSpec = if (stage == TEST_STAGE) {
            appCenterApi.getTestingResourceSpec(appName)
        } else {
            userLabelService.getAppResourceSpec(appName)
        } ?: userLabelService.getAppResourceSpec(appName)
        return mapOf(
            "resources" to mapOf(
                "requests" to mapOf(
                    "cpu" to resourceSpec.cpu,
                    "memory" to resourceSpec.memory,
                    "disk" to resourceSpec.disk,
                    "gpu" to resourceSpec.gpu
                ),
                "limits" to mapOf(
                    "cpu" to resourceSpec.cpu,
                    "memory" to resourceSpec.memory,
                    "disk" to resourceSpec.disk,
                    "gpu" to resourceSpec.gpu
                )
            )
        )
    }

    /**
     * 验证是否存在针对相同的特性&重复范围的注入规则
     */
    private fun checkRepetitiveFeatureImportForSameScope(
        matchScopeDataList: List<MatchScopeDataReqDto>,
        resourceObjectFeatureKey: String
    ) {
        matchScopeDataList.forEach { matchScopeDataReqDto ->
            matchScopeService.findMatchScopeByExternalAndResourceObjectFeatureKey(
                matchScopeDataReqDto,
                resourceObjectFeatureKey
            )?.let {
                throw ResourceObjectException(
                    "资源对象特性KEY:${resourceObjectFeatureKey}针对范围[${
                        JsonUtils.writeValueAsString(
                            matchScopeDataReqDto
                        )
                    }]注入规则已存在，禁止重复设置."
                )
            }
        }
    }

    companion object {
        const val DEFAULT_HCRM_TRAIT_JSONSCHEMA = """{
  "title": "HCRM标签",
  "type": "object",
  "properties": {
    "value": {
      "title": "HCRM标签值",
      "type": "string"
    }
  }
}"""
        const val RESOURCE_OBJECT_FEATURE_KEY_LENGTH = 32
        const val COMMA = ","
        const val INPUT_USER_PARAMS_KEY = "user"
        const val INPUT_SYSTEM_PARAMS_KEY = "system"
        fun getTraitKeyList(traitModifyReqDto: TraitModifyReqDto): List<String> {
            val ret = mutableListOf<String>()
            if (traitModifyReqDto.addingTraitList?.isNotEmpty() ?: false) {
                ret.addAll(traitModifyReqDto.addingTraitList!!.map { it.traitKey })
            }
            if (traitModifyReqDto.updatingTraitList?.isNotEmpty() ?: false) {
                ret.addAll(traitModifyReqDto.updatingTraitList!!.map { it.traitKey })
            }
            if (traitModifyReqDto.createOrUpdateTraitList?.isNotEmpty() ?: false) {
                ret.addAll(traitModifyReqDto.createOrUpdateTraitList!!.map { it.traitKey })
            }
            if (traitModifyReqDto.deletingTraitList?.isNotEmpty() ?: false) {
                ret.addAll(traitModifyReqDto.deletingTraitList!!.map { it.traitKey })
            }
            return ret.distinct()
        }

        fun buildActionType(traitModifyReqDto: TraitModifyReqDto): String {
            val ret = mutableListOf<String>()
            if (traitModifyReqDto.addingTraitList?.isNotEmpty() ?: false) {
                ret.add(ActionEnum.CREATE.name)
            }
            if (traitModifyReqDto.updatingTraitList?.isNotEmpty() ?: false) {
                ret.add(ActionEnum.UPDATE.name)
            }
            if (traitModifyReqDto.createOrUpdateTraitList?.isNotEmpty() ?: false) {
                ret.add(ActionEnum.UPDATE.name)
            }
            if (traitModifyReqDto.deletingTraitList?.isNotEmpty() ?: false) {
                ret.add(ActionEnum.DELETE.name)
            }
            return ret.joinToString(separator = ",")
        }
    }
}