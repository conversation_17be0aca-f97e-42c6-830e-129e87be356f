package com.alibaba.koastline.multiclusters.external

import com.alibaba.koastline.multiclusters.common.config.ExternalCallDowngradeProperties
import com.alibaba.koastline.multiclusters.common.exceptions.GropException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.HttpClientUtils
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.common.utils.SignUtils
import com.alibaba.koastline.multiclusters.external.annotation.ExternalCall
import com.alibaba.koastline.multiclusters.external.model.CpuShareRespData
import com.alibaba.koastline.multiclusters.external.model.CpuShareStatus
import com.alibaba.koastline.multiclusters.external.model.GropResp
import com.alibaba.koastline.multiclusters.resourceobj.model.DispatchLabelDTO
import com.alibaba.koastline.multiclusters.resourceobj.model.DispatchLabelValueDTO
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
class GropApi(val objectMapper: ObjectMapper) {
    val log by logger()
    @Value("\${grop.host}")
    lateinit var host: String
    @Value("\${grop.ak}")
    lateinit var account: String
    @Value("\${grop.sk}")
    lateinit var accessKey: String
    @Autowired
    lateinit var externalCallDowngradeProperties: ExternalCallDowngradeProperties

    @ExternalCall(SYS_CALLED)
    fun queryCpuShare(appName: String, resourceGroup: String, unit: String, stage: String, site: String) : Boolean {
        if (externalCallDowngradeProperties.isDowngrade(SYS_CALLED, "queryCpuShare")) {
            log.info("$SYS_CALLED queryCpuShare downgrade.")
            return false
        }
        val url = "${host}/api/config/dispatch/getSigmaConfigMap"
        val params = mutableMapOf(
            "appName" to appName,
            "groupName" to resourceGroup,
            "unit" to unit,
            "env" to stage,
            "idc" to site,
        )
        val timestamp = SignUtils.getISOTimestamp()
        val headers = mutableMapOf(
            "Content-Type" to "application/json",
            "Hcrm-Tenant-Id" to "alibaba",
            "Hcrm-User-Id" to "264071",
            "Hcrm-Access-Key" to account,
            "Hcrm-Timestamp" to timestamp,
            "Hcrm-Token" to SignUtils.getGropToken(account, accessKey,timestamp)
        )

        val rs = HttpClientUtils.httpGetWithHeaders(url, params, null, headers)
        val gropResp = objectMapper.readValue<GropResp<String>>(rs)
        if (!gropResp.success) {
            val errMsg = "${GropApi.SYS_CALLED} queryCpuShare failed, appName:$appName, resourceGroup:$resourceGroup, unit:$unit, stage:$stage, site:$site, resp:${gropResp.message}"
            throw GropException(errMsg)
        }
        return gropResp.data?.run {
            JsonUtils.readValue(this, CpuShareRespData::class.java)
        }?.data?.allocSpec?.run {
                JsonUtils.readValue(this, CpuShareStatus::class.java)
        }?.cpuSetMode.equals("share")

    }

    @ExternalCall(SYS_CALLED)
    fun getLabelById(id: Long): DispatchLabelDTO? {
        val url = "${host}/api/config/dispatch/getLabelById"
        val params = mutableMapOf(
            "id" to id.toString(),
        )
        val timestamp = SignUtils.getISOTimestamp()
        val headers = mutableMapOf(
            "Content-Type" to "application/json",
            "Hcrm-Tenant-Id" to "alibaba",
            "Hcrm-User-Id" to "264071",
            "Hcrm-Access-Key" to account,
            "Hcrm-Timestamp" to timestamp,
            "Hcrm-Token" to SignUtils.getGropToken(account, accessKey, timestamp)
        )

        val rs = HttpClientUtils.httpGetWithHeaders(url, params, null, headers)
        val gropResp = objectMapper.readValue<GropResp<DispatchLabelDTO>>(rs)
        if (!gropResp.success) {
            val errMsg = "${GropApi.SYS_CALLED} getLabelById failed, resp:${gropResp.message}"
            throw GropException(errMsg)
        }
        return gropResp.data

    }

    @ExternalCall(SYS_CALLED)
    fun getLabelValueById(id: Long): DispatchLabelValueDTO? {
        val url = "${host}/api/config/dispatch/getLabelValueById"
        val params = mutableMapOf(
            "id" to id.toString(),
        )
        val timestamp = SignUtils.getISOTimestamp()
        val headers = mutableMapOf(
            "Content-Type" to "application/json",
            "Hcrm-Tenant-Id" to "alibaba",
            "Hcrm-User-Id" to "264071",
            "Hcrm-Access-Key" to account,
            "Hcrm-Timestamp" to timestamp,
            "Hcrm-Token" to SignUtils.getGropToken(account, accessKey, timestamp)
        )

        val rs = HttpClientUtils.httpGetWithHeaders(url, params, null, headers)
        val gropResp = objectMapper.readValue<GropResp<DispatchLabelValueDTO>>(rs)
        if (!gropResp.success) {
            val errMsg = "${GropApi.SYS_CALLED} getLabelValueById failed, resp:${gropResp.message}"
            throw GropException(errMsg)
        }
        return gropResp.data

    }

    private fun addSignToParams(params: MutableMap<String, String>) {
        val timeStamp = SignUtils.getISOTimestamp();
        // 将时间戳和签名附加到 http 请求参数中
        params["_signature"] = SignUtils.getCapacityToken(account, accessKey, timeStamp, params);
        params["_timestamp"] = timeStamp;
    }

    companion object {
        const val SYS_CALLED = "grop"
    }
}