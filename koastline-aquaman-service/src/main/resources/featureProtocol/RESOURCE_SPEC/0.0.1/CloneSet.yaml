spec:
  template:
    metadata:
      annotations:
        sigma.ali/app-storage-mode: "yundisk-pv"
        sigma.ali/app-storage-size: "${user.resources.requests.disk}"
    spec:
      containers:
        - name: main
          resources:
            requests:
              cpu: "${user.resources.requests.cpu}"
              memory: "${user.resources.requests.memory}"
              <#if user.resources.requests.gpu?? && user.resources.requests.gpu?trim?length gt 0>
              alibabacloud.com/gpu: "${((user.resources.requests.gpu?number)*100)?int}"
              </#if>
            limits:
              cpu: "${user.resources.limits.cpu}"
              memory: "${user.resources.limits.memory}"
              <#if user.resources.limits.gpu?? && user.resources.requests.gpu?trim?length gt 0>
              alibabacloud.com/gpu: "${((user.resources.limits.gpu?number)*100)?int}"
              </#if>