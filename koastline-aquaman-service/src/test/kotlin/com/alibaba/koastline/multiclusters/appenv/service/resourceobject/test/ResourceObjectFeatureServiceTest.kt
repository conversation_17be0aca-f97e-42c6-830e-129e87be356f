package com.alibaba.koastline.multiclusters.appenv.service.resourceobject.test

import com.alibaba.koastline.multiclusters.apre.common.MatchScopeService
import com.alibaba.koastline.multiclusters.apre.model.MatchScopeDataDO
import com.alibaba.koastline.multiclusters.apre.model.Restriction
import com.alibaba.koastline.multiclusters.apre.model.req.MatchScopeDataReqDto
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeTargetTypeEnum
import com.alibaba.koastline.multiclusters.common.RedisService
import com.alibaba.koastline.multiclusters.common.config.CommonProperties.Companion.LOCAL_APP_2_GROUP
import com.alibaba.koastline.multiclusters.common.exceptions.ResourceObjectException
import com.alibaba.koastline.multiclusters.common.utils.FreemarkerUtils
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils.toJson
import com.alibaba.koastline.multiclusters.common.utils.YamlUtils
import com.alibaba.koastline.multiclusters.data.vo.env.ConfigDispatchLabelValueWithMetadata
import com.alibaba.koastline.multiclusters.data.vo.env.ResourceObjectFeature
import com.alibaba.koastline.multiclusters.data.vo.env.ResourceObjectFeatureImport
import com.alibaba.koastline.multiclusters.data.vo.env.ResourceObjectFeatureImportWithMatchScopeCombine
import com.alibaba.koastline.multiclusters.data.vo.env.ResourceObjectFeatureProtocol
import com.alibaba.koastline.multiclusters.event.Event
import com.alibaba.koastline.multiclusters.event.EventProducer
import com.alibaba.koastline.multiclusters.event.ResourceSpecChangeEventPayload
import com.alibaba.koastline.multiclusters.external.model.AppInfo
import com.alibaba.koastline.multiclusters.external.model.AppLevelEnum
import com.alibaba.koastline.multiclusters.external.model.AppStatusEnum
import com.alibaba.koastline.multiclusters.resourceobj.ResourceObjectFeatureService
import com.alibaba.koastline.multiclusters.resourceobj.model.*
import com.alibaba.koastline.multiclusters.resourceobj.model.req.*
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectConstants.ENV_LABELS_FEATURE_KEY
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectConstants.GPU_AFFINITY_SPEC_FEATURE_KEY
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectConstants.RESOURCE_SPEC_FEATURE_KEY
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectFeatureDisplayThemeEnum
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectFeatureEffectiveStageEnum
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectFeatureImportStatusEnum
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectFeatureTypeEnum
import com.alibaba.koastline.multiclusters.resourceobj.specific.ResourceSpecFeatureService
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.*
import org.junit.Assert
import org.junit.Rule
import org.junit.Test
import org.junit.jupiter.api.Assertions.*
import org.junit.rules.ExpectedException
import testutils.BaseTest
import java.lang.reflect.InvocationTargetException
import java.time.Instant
import java.util.*
import kotlin.test.assertEquals


class ResourceObjectFeatureServiceTest: BaseTest() {
    @JvmField
    @Rule
    val exceptionRule: ExpectedException = ExpectedException.none()

    val now = Date(Instant.now().toEpochMilli())


    @Test
    fun assembleSpecByFeatureImportTest() {
        val resourecFeaturreImport = ResourceObjectFeatureImport(
            null, RESOURCE_SPEC_FEATURE_KEY, ResourceObjectFeatureImportStatusEnum.ENABLED.name, """resources:
  requests:
    cpu: '4'
    memory: 8Gi
    disk: 60Gi
    gpu: '1'
  limits:
    cpu: '4'
    memory: 8Gi
    disk: 60Gi
    gpu: '1'
""".trimIndent(), "000001", "000001", now, now, "N"
        )

        val extraTemplateFunc = mapOf("FreemarkerUtils" to FreemarkerUtils)
        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())).apply {

            every {
                resourceObjectFeatureRepo.findByResourceObjectFeatureKeyList(any())
            } returns listOf(
                ResourceObjectFeature(
                    1, RESOURCE_SPEC_FEATURE_KEY, "资源规格",
                    "[{\"scene\":\"SCALE_OUT\",\"buildType\":\"CREATE\"},{\"scene\":\"SCALE_OUT\",\"buildType\":\"PATCH\"}]",
                    "000001",
                    "000001",
                    now,
                    now,
                    "N",
                    "StatefulSet,CloneSet",
                    "INPUT",
                    "AFTER_VERSIONOUT",
                    null,
                    "TREE"
                )
            )
            every {
                featureImportHookManager.preProcess(any())
            } returnsArgument 0
            every { featureProtocolLoadService.getFeatureProtocol(any()) } returns """spec:
  containers:
    - name: main
      resources:
        requests:
          cpu: ${'$'}{user.resources.requests.cpu}
          memory: ${'$'}{user.resources.requests.memory}
          ephemeral-storage: ${'$'}{user.resources.requests.disk}
          <#if user.resources.requests.gpu??>
          nvidia.com/gpu: ${'$'}{user.resources.requests.gpu}
          </#if>
        limits:
          cpu: ${'$'}{user.resources.limits.cpu}
          memory: ${'$'}{user.resources.limits.memory}
          ephemeral-storage: ${'$'}{user.resources.limits.disk}
          <#if user.resources.limits.gpu??>
          nvidia.com/gpu: ${'$'}{user.resources.limits.gpu}
          </#if>""".trimIndent()
            every {
                resourceObjectFeatureProtocolRepo.findByResourceObjectFeatureKeyAndVersion(
                    RESOURCE_SPEC_FEATURE_KEY,
                    "0.0.1"
                )
            } returns listOf(
                ResourceObjectFeatureProtocol(
                    1, RESOURCE_SPEC_FEATURE_KEY, "StatefulSet", "0.0.1", """spec:
  containers:
    - name: main
      resources:
        requests:
          cpu: ${'$'}{user.resources.requests.cpu}
          memory: ${'$'}{user.resources.requests.memory}
          ephemeral-storage: ${'$'}{user.resources.requests.disk}
          <#if user.resources.requests.gpu??>
          nvidia.com/gpu: ${'$'}{user.resources.requests.gpu}
          </#if>
        limits:
          cpu: ${'$'}{user.resources.limits.cpu}
          memory: ${'$'}{user.resources.limits.memory}
          ephemeral-storage: ${'$'}{user.resources.limits.disk}
          <#if user.resources.limits.gpu??>
          nvidia.com/gpu: ${'$'}{user.resources.limits.gpu}
          </#if>""".trimIndent(), "000001", "000001", now, now, "N"
                )
            )
        }


        val featureSpecList = resourceObjectFeatureService.assembleSpecByFeatureImport(
            resourceObjectFeatureImportList = listOf(resourecFeaturreImport),
            protocol = "StatefulSet",
            version = null,
            systemInputParams = emptyMap(),
            extraTemplateFunc = extraTemplateFunc
        )
        assertEquals(1, featureSpecList.size)
        assertEquals(
            """spec:
  containers:
    - name: main
      resources:
        requests:
          cpu: 4
          memory: 8Gi
          ephemeral-storage: 60Gi
          nvidia.com/gpu: 1
        limits:
          cpu: 4
          memory: 8Gi
          ephemeral-storage: 60Gi
          nvidia.com/gpu: 1
""", featureSpecList[0].patch
        )
    }

    @Test
    fun `createOrUpdateResourceSpec -- send event by app`() {
        val externalType = "APPLICATION"
        val externalId = "a"
        val employeeId = "xeid"
        val resourceSpec = ResourceSpec("4", "8", "60")

        val createFeatureImportCap = slot<ResourceObjectFeatureImportCreateReqDto>()
        val eventCap = slot<Event<ResourceSpecChangeEventPayload>>()
        val service = spyk(ResourceSpecFeatureService()).apply {
            resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())) {
                every {
                    getFeatureImportByMatchScopeAndFeatureKey(
                        externalType,
                        externalId,
                        RESOURCE_SPEC_FEATURE_KEY
                    )
                } returns null
                every {
                    createFeatureImport(capture(createFeatureImportCap))
                } returns -1L
            }
            eventProducer = spyk(EventProducer(mockk(), mockk())) {
                every { sendIgnoreError(capture(eventCap)) } just runs
            }
            every {
                redisService.setValue(any(), any())
            } just runs
        }

        val got =
            service.createOrUpdateResourceSpec(externalType, externalId, resourceSpec, employeeId)
        assertEquals(got.cpu, "4")
        assertEquals(eventCap.captured.data.appName, "a")
        assertEquals(eventCap.captured.data.resourceSpec.cpu, "4")
    }

    @Test
    fun `createOrUpdateResourceSpec -- send event by resourceGroup`() {
        val externalType = "RESOURCE_GROUP"
        val externalId = "ag"
        val employeeId = "xeid"
        val resourceSpec = ResourceSpec("4", "8", "60")

        val createFeatureImportCap = slot<ResourceObjectFeatureImportCreateReqDto>()
        val eventCap = slot<Event<ResourceSpecChangeEventPayload>>()
        val service = spyk(ResourceSpecFeatureService()).apply {
            resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())) {
                every {
                    getFeatureImportByMatchScopeAndFeatureKey(
                        externalType,
                        externalId,
                        RESOURCE_SPEC_FEATURE_KEY
                    )
                } returns null
                every {
                    createFeatureImport(capture(createFeatureImportCap))
                } returns -1L
            }
            eventProducer = spyk(EventProducer(mockk(), mockk())) {
                every { sendIgnoreError(capture(eventCap)) } just runs
            }
            redisService = mockk<RedisService>() {
                every {
                    setValue(any(), any())
                } just runs
            }
        }

        val got =
            service.createOrUpdateResourceSpec(externalType, externalId, resourceSpec, employeeId)
        assertEquals(got.cpu, "4")
        assertEquals(eventCap.captured.data.resourceGroup, "ag")
        assertEquals(eventCap.captured.data.resourceSpec.cpu, "4")
    }

    @Test(expected = ResourceObjectException::class)
    fun testDeleteResourceSpecForCpuModelByResourceGroup() {
        val matchScopeExternalType = MatchScopeExternalTypeEnum.RESOURCE_GROUP.name
        val matchScopeExternalId = "normandy_test_app4host"
        val service = spyk(ResourceSpecFeatureService()).apply {
            every {
                getResourceSpecByMatchScope(matchScopeExternalType, matchScopeExternalId)
            }returns ResourceSpec(
                cpu = "4", memory = "8", disk = "60", gpu = "1"
            )
        }
        service.deleteResourceSpecForCpuModelByResourceGroup(resourceGroup = matchScopeExternalId, "10000")
    }

    @Test
    fun testListResourceObjectFeatureImportSpec() {
        val now = Date(Instant.now().toEpochMilli())
        val appName = "normandy-test-app4"
        val clusterId = "cdscdcsdcsdcsdcs"
        val resourceGroup = "normandy-test-app4_prehost"
        val matchScopeTargetType = MatchScopeTargetTypeEnum.ResourceObjectFeatureImport.name
        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())).apply {
            this.resourceSpecFeatureService = spyk(ResourceSpecFeatureService())
            this.matchScopeService = spyk(MatchScopeService(ObjectMapper())) {
                every {
                    findMatchScopesByTargetAndExternalForApp(matchScopeTargetType, appName, listOf(resourceGroup), "envStackId", clusterId = clusterId)
                } returns listOf(
                    MatchScopeDataDO(1,1,matchScopeTargetType,"1#2_3", MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name,creator = "admin", modifier = "admin"),
                    MatchScopeDataDO(2,2,matchScopeTargetType,"1", MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name,creator = "admin", modifier = "admin"),
                    MatchScopeDataDO(3,3,matchScopeTargetType,"alibaba", MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name,creator = "admin", modifier = "admin"),
                    MatchScopeDataDO(4,4,matchScopeTargetType,resourceGroup, MatchScopeExternalTypeEnum.RESOURCE_GROUP.name,creator = "admin", modifier = "admin"),
                    MatchScopeDataDO(5,5,matchScopeTargetType,appName, MatchScopeExternalTypeEnum.APPLICATION.name,creator = "admin", modifier = "admin")
                )
            }
        }.apply {
            every { featureProtocolLoadService.getFeatureProtocol(any(), any()) } returns """spec:
  containers:
    - name: main
      resources:
        requests:
          cpu: ${'$'}{user.resources.requests.cpu}
          memory: ${'$'}{user.resources.requests.memory}
          ephemeral-storage: ${'$'}{user.resources.requests.disk}
          <#if user.resources.requests.gpu??>
          nvidia.com/gpu: ${'$'}{user.resources.requests.gpu}
          </#if>
        limits:
          cpu: ${'$'}{user.resources.limits.cpu}
          memory: ${'$'}{user.resources.limits.memory}
          ephemeral-storage: ${'$'}{user.resources.limits.disk}
          <#if user.resources.limits.gpu??>
          nvidia.com/gpu: ${'$'}{user.resources.limits.gpu}
          </#if>""".trimIndent()
            every {
                resourceObjectFeatureImportRepo.findById(any())
            } returns ResourceObjectFeatureImport(
                null,RESOURCE_SPEC_FEATURE_KEY,ResourceObjectFeatureImportStatusEnum.ENABLED.name,"""resources:
  requests:
    cpu: '4'
    memory: 8Gi
    disk: 60Gi
    gpu: '1'
  limits:
    cpu: '4'
    memory: 8Gi
    disk: 60Gi
    gpu: '1'
""".trimIndent(), "000001", "000001", now, now, "N")
            every {
                resourceObjectFeatureRepo.findByResourceObjectFeatureKeyList(any())
            }returns listOf(
                ResourceObjectFeature(1,RESOURCE_SPEC_FEATURE_KEY,"资源规格",
                    "[{\"scene\":\"SCALE_OUT\",\"buildType\":\"CREATE\"},{\"scene\":\"SCALE_OUT\",\"buildType\":\"PATCH\"}]",
                    "000001",
                    "000001",
                    now,
                    now,
                    "N",
                    "StatefulSet,CloneSet",
                    "INPUT",
                    "AFTER_VERSIONOUT",
                    null,
                    "TREE"
                )
            )
            every {
                featureImportHookManager.preProcess(any())
            } returnsArgument 0

            every {
                resourceObjectFeatureProtocolRepo.findByResourceObjectFeatureKeyAndVersion(
                    RESOURCE_SPEC_FEATURE_KEY,
                    "0.0.1"
                )
            } returns listOf(
                ResourceObjectFeatureProtocol(
                    1, RESOURCE_SPEC_FEATURE_KEY, "StatefulSet", "0.0.1", """spec:
  containers:
    - name: main
      resources:
        requests:
          cpu: ${'$'}{user.resources.requests.cpu}
          memory: ${'$'}{user.resources.requests.memory}
          ephemeral-storage: ${'$'}{user.resources.requests.disk}
          <#if user.resources.requests.gpu??>
          nvidia.com/gpu: ${'$'}{user.resources.requests.gpu}
          </#if>
        limits:
          cpu: ${'$'}{user.resources.limits.cpu}
          memory: ${'$'}{user.resources.limits.memory}
          ephemeral-storage: ${'$'}{user.resources.limits.disk}
          <#if user.resources.limits.gpu??>
          nvidia.com/gpu: ${'$'}{user.resources.limits.gpu}
          </#if>""".trimIndent(),"000001","000001",now,now,"N")
            )
            every {gropApi.queryCpuShare(any(), any(), any(), any(), any()) }returns false
            every {jmenvApi.getMiddlewareEnv(any(), any())} returns "MW_ENV"
        }
        val featureSpecList = resourceObjectFeatureService.listResourceObjectFeatureImportSpec(
            workloadMetadataConstraint = WorkloadMetadataConstraint(appName,resourceGroup,"na610","center","PUBLISH","default",clusterId,"aaaaaaaaaaa"),
            resourceObjectSceneEnum = ResourceObjectSceneEnum.SCALE_OUT,
            resourceObjectBuildTypeEnum = ResourceObjectBuildTypeEnum.CREATE,
            protocol = "StatefulSet",
            version = null,
            systemInputParams = emptyMap(),
            envStackId = "envStackId"
        )
        assertEquals(1, featureSpecList.size)
        assertEquals("""spec:
  containers:
    - name: main
      resources:
        requests:
          cpu: 4
          memory: 8589934592
          ephemeral-storage: 64424509440
          nvidia.com/gpu: 1
        limits:
          cpu: 4
          memory: 8589934592
          ephemeral-storage: 64424509440
          nvidia.com/gpu: 1
""", featureSpecList[0].patch
        )
    }


    @Test
    fun `testListResourceObjectFeatureImportSpec -- Gpu`() {
        val now = Date(Instant.now().toEpochMilli())
        val appName = "pouchapp"
        val clusterId = "cdscdcsdcsdcsdcs"
        val resourceGroup = "pouchapp_preet2_prehost"
        val matchScopeTargetType = MatchScopeTargetTypeEnum.ResourceObjectFeatureImport.name
        val gpuProtocol = ResourceObjectFeatureProtocol(
            1, GPU_AFFINITY_SPEC_FEATURE_KEY, "StatefulSet", "0.0.1", """
spec:
  template:
    spec:
      affinity: 
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: alibabacloud.com/gpu-card-model-detail
                    operator: In
                    values:
<#list user.gpu.models as item>
                      - ${'$'}{item}
</#list>""".trimIndent(), "000001", "000001", now, now, "N"
        )
        val resourceSpecProtocol = ResourceObjectFeatureProtocol(
            1, RESOURCE_SPEC_FEATURE_KEY, "StatefulSet", "0.0.1", """spec:
  containers:
    - name: main
      resources:
        requests:
          cpu: ${'$'}{user.resources.requests.cpu}
          memory: ${'$'}{user.resources.requests.memory}
          ephemeral-storage: ${'$'}{user.resources.requests.disk}
          <#if user.resources.requests.gpu??>
          nvidia.com/gpu: ${'$'}{user.resources.requests.gpu}
          </#if>
        limits:
          cpu: ${'$'}{user.resources.limits.cpu}
          memory: ${'$'}{user.resources.limits.memory}
          ephemeral-storage: ${'$'}{user.resources.limits.disk}
          <#if user.resources.limits.gpu??>
          nvidia.com/gpu: ${'$'}{user.resources.limits.gpu}
          </#if>""".trimIndent(), "000001", "000001", now, now, "N"
        )
        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())).apply {
            this.resourceSpecFeatureService = spyk(ResourceSpecFeatureService())
            this.matchScopeService = spyk(MatchScopeService(ObjectMapper())) {
                every {
                    findMatchScopesByTargetAndExternalForApp(matchScopeTargetType, appName, listOf(resourceGroup), "envStackId", clusterId = clusterId)
                } returns listOf(
                    MatchScopeDataDO(
                        4,
                        18,
                        matchScopeTargetType,
                        resourceGroup,
                        MatchScopeExternalTypeEnum.RESOURCE_GROUP.name,
                        creator = "admin",
                        modifier = "admin"
                    ),
                    MatchScopeDataDO(
                        4,
                        4,
                        matchScopeTargetType,
                        resourceGroup,
                        MatchScopeExternalTypeEnum.RESOURCE_GROUP.name,
                        creator = "admin",
                        modifier = "admin"
                    )
                )
                every {gropApi.queryCpuShare(any(), any(), any(), any(), any()) }returns false
                every {jmenvApi.getMiddlewareEnv(any(), any())} returns "MW_ENV"
            }
        }.apply {
            every {
                resourceObjectFeatureImportRepo.findById(18)
            } returns ResourceObjectFeatureImport(
                18, GPU_AFFINITY_SPEC_FEATURE_KEY, ResourceObjectFeatureImportStatusEnum.ENABLED.name, """
gpu:
  models: [Tesla-V100-SXM2-16GB, T4]
""".trimIndent(), "000001", "000001", now, now, "N"
            )
            every {
                resourceObjectFeatureImportRepo.findById(4)
            } returns ResourceObjectFeatureImport(
                null, RESOURCE_SPEC_FEATURE_KEY, ResourceObjectFeatureImportStatusEnum.ENABLED.name, """resources:
  requests:
    cpu: '4'
    memory: 8Gi
    disk: 60Gi
    gpu: '1'
  limits:
    cpu: '4'
    memory: 8Gi
    disk: 60Gi
    gpu: '1'
""".trimIndent(), "000001", "000001", now, now, "N"
            )
            every {
                resourceObjectFeatureRepo.findByResourceObjectFeatureKeyList(
                    listOf(
                        GPU_AFFINITY_SPEC_FEATURE_KEY,
                        RESOURCE_SPEC_FEATURE_KEY,
                        ENV_LABELS_FEATURE_KEY,
                    )
                )
            } returns listOf(
                ResourceObjectFeature(
                    1, GPU_AFFINITY_SPEC_FEATURE_KEY, "GPU亲和",
                    "[{\"scene\":\"SCALE_OUT\",\"buildType\":\"CREATE\"},{\"scene\":\"SCALE_OUT\",\"buildType\":\"PATCH\"},{\"scene\":\"DEPLOY\",\"buildType\":\"PATCH\"}]",
                    "000001",
                    "000001",
                    now,
                    now,
                    "N",
                    "StatefulSet,CloneSet",
                    "INPUT",
                    "AFTER_VERSIONOUT",
                    null,
                    "TREE"
                ),
                ResourceObjectFeature(
                    1, RESOURCE_SPEC_FEATURE_KEY, "资源规格",
                    "[{\"scene\":\"SCALE_OUT\",\"buildType\":\"CREATE\"},{\"scene\":\"SCALE_OUT\",\"buildType\":\"PATCH\"}]",
                    "000001",
                    "000001",
                    now,
                    now,
                    "N",
                    "StatefulSet,CloneSet",
                    "INPUT",
                    "AFTER_VERSIONOUT",
                    null,
                    "TREE"
                )
            )
            every { featureProtocolLoadService.getFeatureProtocol(gpuProtocol, any()) } returns gpuProtocol.patch
            every {
                featureProtocolLoadService.getFeatureProtocol(
                    resourceSpecProtocol,
                    any()
                )
            } returns resourceSpecProtocol.patch
            every {
                resourceObjectFeatureProtocolRepo.findByResourceObjectFeatureKeyAndVersion(
                    GPU_AFFINITY_SPEC_FEATURE_KEY,
                    "0.0.1"
                )
            } returns listOf(
                ResourceObjectFeatureProtocol(
                    1, GPU_AFFINITY_SPEC_FEATURE_KEY, "StatefulSet", "0.0.1", """
spec:
  template:
    spec:
      affinity: 
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: alibabacloud.com/gpu-card-model-detail
                    operator: In
                    values:
<#list user.gpu.models as item>
                      - ${'$'}{item}
</#list>""".trimIndent(), "000001", "000001", now, now, "N"
                )
            )
            every {
                featureImportHookManager.preProcess(any())
            } returnsArgument 0
            every {
                resourceObjectFeatureProtocolRepo.findByResourceObjectFeatureKeyAndVersion(
                    RESOURCE_SPEC_FEATURE_KEY,
                    "0.0.1"
                )
            } returns listOf(
                ResourceObjectFeatureProtocol(
                    1, RESOURCE_SPEC_FEATURE_KEY, "StatefulSet", "0.0.1", """spec:
  containers:
    - name: main
      resources:
        requests:
          cpu: ${'$'}{user.resources.requests.cpu}
          memory: ${'$'}{user.resources.requests.memory}
          ephemeral-storage: ${'$'}{user.resources.requests.disk}
          <#if user.resources.requests.gpu??>
          nvidia.com/gpu: ${'$'}{user.resources.requests.gpu}
          </#if>
        limits:
          cpu: ${'$'}{user.resources.limits.cpu}
          memory: ${'$'}{user.resources.limits.memory}
          ephemeral-storage: ${'$'}{user.resources.limits.disk}
          <#if user.resources.limits.gpu??>
          nvidia.com/gpu: ${'$'}{user.resources.limits.gpu}
          </#if>""".trimIndent(), "000001", "000001", now, now, "N"
                )
            )

        }
        val featureSpecList = resourceObjectFeatureService.listResourceObjectFeatureImportSpec(
            workloadMetadataConstraint = WorkloadMetadataConstraint(
                appName,
                resourceGroup,
                "na610",
                "center",
                "PUBLISH",
                "default",
                clusterId,
                "aaaaaaaaaaa"
            ),
            resourceObjectSceneEnum = ResourceObjectSceneEnum.SCALE_OUT,
            resourceObjectBuildTypeEnum = ResourceObjectBuildTypeEnum.CREATE,
            protocol = "StatefulSet",
            version = null,
            systemInputParams = emptyMap(),
            envStackId = "envStackId"
        )
        assertEquals(2, featureSpecList.size)
        assertEquals(
            """spec:
  containers:
    - name: main
      resources:
        requests:
          cpu: 4
          memory: 8589934592
          ephemeral-storage: 64424509440
          nvidia.com/gpu: 1
        limits:
          cpu: 4
          memory: 8589934592
          ephemeral-storage: 64424509440
          nvidia.com/gpu: 1
""", featureSpecList[1].patch
        )
        assertEquals(
            """spec:
  template:
    spec:
      affinity: 
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: alibabacloud.com/gpu-card-model-detail
                    operator: In
                    values:
                      - Tesla-V100-SXM2-16GB
                      - T4
""", featureSpecList[0].patch
        )
    }


    @Test(expected = ResourceObjectException::class)
    fun testCreateFeature_with_duplicate_key() {
        val resourceObjectFeatureKey = "resourceObjectFeatureKey"
        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())) {
            every {
                resourceObjectFeatureRepo.findByResourceObjectFeatureKeyAndVersion(resourceObjectFeatureKey, "0.0.1")
            } returns ResourceObjectFeature(
                1L,
                resourceObjectFeatureKey,
                "title",
                "[{\"scene\":\"SCALE_OUT\",\"buildType\":\"CREATE\"},{\"scene\":\"SCALE_OUT\",\"buildType\":\"PATCH\"}]",
                "",
                "",
                now,
                now,
                "N",
                "StatefulSet,CloneSet", "INPUT", "AFTER_VERSIONOUT", null, "TREE"
            )
        }
        resourceObjectFeatureService.createFeature(
            ResourceObjectFeatureCreateReqDto(
                resourceObjectFeatureKey,
                "title",
                listOf(),
                "creator",
                "StatefulSet,CloneSet",
                ResourceObjectFeatureTypeEnum.INPUT, ResourceObjectFeatureEffectiveStageEnum.AFTER_VERSIONOUT
            )
        )
    }

    @Test(expected = ResourceObjectException::class)
    fun testUpdateFeature_while_is_deleted() {
        val resourceObjectFeatureId = 1L
        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())) {
            every {
                resourceObjectFeatureRepo.findById(resourceObjectFeatureId)
            } returns ResourceObjectFeature(
                1L,
                "resourceObjectFeatureKey",
                "title",
                "[{\"scene\":\"SCALE_OUT\",\"buildType\":\"CREATE\"},{\"scene\":\"SCALE_OUT\",\"buildType\":\"PATCH\"}]",
                "",
                "",
                now,
                now,
                "Y",
                "StatefulSet,CloneSet",
                "INPUT",
                "AFTER_VERSIONOUT",
                null,
                "TREE"
            )
        }
        resourceObjectFeatureService.updateFeature(
            ResourceObjectFeatureUpdateReqDto(
                resourceObjectFeatureId,
                null,
                "title",
                listOf(),
                "modifier",
                "StatefulSet,CloneSet"
            )
        )
    }

    @Test
    fun test_FilterByAppInfo() {
        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())) {
            every {
                matchScopeService.findFeatureMatchScopeByResourceObjectFeatureKey(any(), any())
            } returns listOf(
                MatchScopeDataDO(
                    externalId = "global-aserver-ingress",
                    externalType = MatchScopeExternalTypeEnum.APPLICATION.name,
                    creator = "admin",
                    modifier = "admin",
                    targetId = 1L
                )
            )
        }
        val assertVal = resourceObjectFeatureService.filterByAppInfo(
            ResourceObjectFeature(
                1, RESOURCE_SPEC_FEATURE_KEY, "资源规格",
                "[{\"scene\":\"SCALE_OUT\",\"buildType\":\"CREATE\"},{\"scene\":\"SCALE_OUT\",\"buildType\":\"PATCH\"}]",
                "000001",
                "000001",
                now,
                now,
                "N",
                "StatefulSet,CloneSet",
                "INPUT",
                "AFTER_VERSIONOUT",
                null,
                "TREE"
            ),
            AppInfo(
                id = 1L,
                buId = 3L,
                name = "app_name",
                productId = 5L,
                productFullLineIdPath = "4_5",
                status = AppStatusEnum.valueOf("ONLINE"),
                level = AppLevelEnum.GRADE4
            )
        )
        assertEquals(false, assertVal)
    }

    @Test(expected = ResourceObjectException::class)
    fun testUpdateFeature_while_is_not_existent() {
        val resourceObjectFeatureId = 1L
        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())) {
            every {
                resourceObjectFeatureRepo.findById(resourceObjectFeatureId)
            } returns null
        }
        resourceObjectFeatureService.updateFeature(
            ResourceObjectFeatureUpdateReqDto(
                resourceObjectFeatureId,
                null,
                "title",
                listOf(),
                "modifier",
                "StatefulSet,CloneSet"
            )
        )
    }

    @Test(expected = ResourceObjectException::class)
    fun testCreateFeatureImport_while_feature_is_not_existent() {
        val resourceObjectFeatureKey = "resourceObjectFeatureKey"
        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())) {
            every {
                resourceObjectFeatureRepo.findByResourceObjectFeatureKeyAndVersion(resourceObjectFeatureKey, "0.0.1")
            } returns null
        }
        resourceObjectFeatureService.createFeatureImport(ResourceObjectFeatureImportCreateReqDto(
            resourceObjectFeatureKey,
            ResourceObjectFeatureImportStatusEnum.ENABLED,
            emptyMap(),
            "creator",
            emptyList()
        ))
    }

    @Test
    fun testCreateFeatureImport_while_repetitive_feature_to_same_scope() {
        val resourceObjectFeatureKey = "resourceObjectFeatureKey"
        val matchScopeData = MatchScopeDataReqDto(
            externalId = "externalId",
            externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name
        )
        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())) {
            every {
                resourceObjectFeatureRepo.findByResourceObjectFeatureKeyAndVersion(resourceObjectFeatureKey, "0.0.1")
            } returns ResourceObjectFeature(
                id = 1L,
                resourceObjectFeatureKey = resourceObjectFeatureKey,
                title = "",
                useScope = "",
                creator = "",
                modifier = "",
                feasibleProtocols = "StatefulSet,CloneSet",
                type = "INPUT",
                effectiveStage = "AFTER_VERSIONOUT",
                displayTheme = "TREE"
            )
            every {
                matchScopeService.findMatchScopeByExternalAndResourceObjectFeatureKey(matchScopeData, resourceObjectFeatureKey)
            } returns MatchScopeDataDO (
                id = 1L,externalId = "externalId", externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name, creator = "creator", modifier = "modifier"
            )
        }

        exceptionRule.expect(ResourceObjectException::class.java)
        exceptionRule.expectMessage("资源对象特性KEY:resourceObjectFeatureKey针对范围[{\"externalId\":\"externalId\",\"externalType\":\"AONE_PRODUCTLINE\"}]注入规则已存在，禁止重复设置.")

        resourceObjectFeatureService.createFeatureImport(ResourceObjectFeatureImportCreateReqDto(
            resourceObjectFeatureKey = resourceObjectFeatureKey,
            status = ResourceObjectFeatureImportStatusEnum.ENABLED,
            paramMap = emptyMap(),
            creator = "creator",
            matchScopeDataList = listOf(matchScopeData)
        ))
    }

    @Test(expected = ResourceObjectException::class)
    fun testUpdateFeatureImport_while_is_deleted() {
        val resourceObjectFeatureImportId = 1L
        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())) {
            every {
                resourceObjectFeatureImportRepo.findById(resourceObjectFeatureImportId)
            } returns ResourceObjectFeatureImport(1L,"resourceObjectFeatureKey", "Enabled","","","",now,now,"Y")
        }
        resourceObjectFeatureService.updateFeatureImport(ResourceObjectFeatureImportUpdateReqDto(resourceObjectFeatureImportId,ResourceObjectFeatureImportStatusEnum.ENABLED , emptyMap(),"modifier",
            emptyList()
        ))
    }

    @Test(expected = ResourceObjectException::class)
    fun testUpdateFeatureImport_while_is_not_existent() {
        val resourceObjectFeatureImportId = 1L
        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())) {
            every {
                resourceObjectFeatureImportRepo.findById(resourceObjectFeatureImportId)
            } returns null
        }
        resourceObjectFeatureService.updateFeatureImport(ResourceObjectFeatureImportUpdateReqDto(resourceObjectFeatureImportId,ResourceObjectFeatureImportStatusEnum.ENABLED , emptyMap(),"modifier",
            emptyList()
        ))
    }

    @Test
    fun testUpdateFeatureImport_while_repetitive_feature_to_same_scope() {
        val resourceObjectFeatureImportId = 1L
        val resourceObjectFeatureKey = "resourceObjectFeatureKey"
        val matchScopeData = MatchScopeDataReqDto(
            externalId = "externalId",
            externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name
        )
        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())) {
            every {
                resourceObjectFeatureImportRepo.findById(resourceObjectFeatureImportId)
            } returns ResourceObjectFeatureImport (
                id = resourceObjectFeatureImportId,resourceObjectFeatureKey = resourceObjectFeatureKey, status = ResourceObjectFeatureImportStatusEnum.ENABLED.name, creator = "creator",  modifier = "modifier", paramMap = null
            )
            justRun {
                matchScopeService.deleteMatchScopeByTarget(resourceObjectFeatureImportId, MatchScopeTargetTypeEnum.ResourceObjectFeatureImport.name, any())
            }
            every {
                matchScopeService.findMatchScopeByExternalAndResourceObjectFeatureKey(matchScopeData, resourceObjectFeatureKey)
            } returns MatchScopeDataDO (
                id = 1L,externalId = "externalId", externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name, creator = "creator", modifier = "modifier"
            )
        }

        exceptionRule.expect(ResourceObjectException::class.java)
        exceptionRule.expectMessage("资源对象特性KEY:resourceObjectFeatureKey针对范围[{\"externalId\":\"externalId\",\"externalType\":\"AONE_PRODUCTLINE\"}]注入规则已存在，禁止重复设置.")

        resourceObjectFeatureService.updateFeatureImport(ResourceObjectFeatureImportUpdateReqDto(
            id = resourceObjectFeatureImportId, status = ResourceObjectFeatureImportStatusEnum.ENABLED , paramMap = emptyMap(), modifier = "modifier",
            matchScopeDataList = listOf(matchScopeData)
        ))
    }

    @Test
    fun testUpdateFeatureImport_while_repetitive_feature_to_same_scope_and_restrictions() {
        val resourceObjectFeatureImportId = 1L
        val resourceObjectFeatureKey = "resourceObjectFeatureKey"
        val matchScopeData = MatchScopeDataReqDto(
            externalId = "externalId",
            externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name,
            restrictions = listOf(
                Restriction(site = "na610")
            )
        )
        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())) {
            every {
                resourceObjectFeatureImportRepo.findById(resourceObjectFeatureImportId)
            } returns ResourceObjectFeatureImport (
                id = resourceObjectFeatureImportId,resourceObjectFeatureKey = resourceObjectFeatureKey, status = ResourceObjectFeatureImportStatusEnum.ENABLED.name, creator = "creator",  modifier = "modifier", paramMap = null
            )
            justRun {
                matchScopeService.deleteMatchScopeByTarget(resourceObjectFeatureImportId, MatchScopeTargetTypeEnum.ResourceObjectFeatureImport.name, any())
            }
            every {
                matchScopeService.findMatchScopeByExternalAndResourceObjectFeatureKey(matchScopeData, resourceObjectFeatureKey)
            } returns MatchScopeDataDO (
                id = 1L,externalId = "externalId", externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name, creator = "creator", modifier = "modifier"
            )
        }

        exceptionRule.expect(ResourceObjectException::class.java)
        exceptionRule.expectMessage("资源对象特性KEY:resourceObjectFeatureKey针对范围[{\"externalId\":\"externalId\",\"externalType\":\"AONE_PRODUCTLINE\",\"restrictions\":[{\"site\":\"na610\",\"clusterIdList\":[]}]}]注入规则已存在，禁止重复设置.")

        resourceObjectFeatureService.updateFeatureImport(ResourceObjectFeatureImportUpdateReqDto(
            id = resourceObjectFeatureImportId, status = ResourceObjectFeatureImportStatusEnum.ENABLED , paramMap = emptyMap(), modifier = "modifier",
            matchScopeDataList = listOf(matchScopeData)
        ))
    }

    @Test(expected = ResourceObjectException::class)
    fun testCreateFeatureProtocol_while_feature_is_not_existent() {
        val resourceObjectFeatureKey = "resourceObjectFeatureKey"
        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())) {
            every {
                resourceObjectFeatureRepo.findByResourceObjectFeatureKeyAndVersion(resourceObjectFeatureKey, "0.0.1")
            } returns null
        }
        resourceObjectFeatureService.createFeatureProtocol(ResourceObjectFeatureProtocolCreateReqDto(
                resourceObjectFeatureKey,
                ResourceObjectProtocolEnum.StatefulSet,
                "",""
            )
        )
    }

    @Test(expected = ResourceObjectException::class)
    fun testUpdateFeatureProtocol_while_is_deleted() {
        val resourceObjectFeatureProtocolId = 1L
        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())) {
            every {
                resourceObjectFeatureProtocolRepo.findById(resourceObjectFeatureProtocolId)
            } returns ResourceObjectFeatureProtocol(
                1L,
                "resourceObjectFeatureKey",
                "StatefulSet",
                "0.0.1",
                "",
                "",
                "",
                now,
                now,
                "Y"
            )
        }
        resourceObjectFeatureService.updateFeatureProtocol(ResourceObjectFeatureProtocolUpdateReqDto(resourceObjectFeatureProtocolId,"","modifier"))
    }

    @Test(expected = ResourceObjectException::class)
    fun testUpdateFeatureProtocol_while_is_not_existent() {
        val resourceObjectFeatureProtocolId = 1L
        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())) {
            every {
                resourceObjectFeatureProtocolRepo.findById(resourceObjectFeatureProtocolId)
            } returns null
        }
        resourceObjectFeatureService.updateFeatureProtocol(ResourceObjectFeatureProtocolUpdateReqDto(resourceObjectFeatureProtocolId,"","modifier"))
    }

    @Test
    fun testComputeFeatureImportMatchScopePriority_with_restriction_diff() {
        val now = Date(Instant.now().toEpochMilli())
        val matchScopeData = MatchScopeDataDO(
            targetId = 1L,
            externalId = "1#2", creator = "10000", modifier = "10000",
            externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name,
            restrictions = listOf(
                Restriction(clusterIdList = listOf("cluster_a01"))
            )
        )
        val featureMap = mutableMapOf(
            RESOURCE_SPEC_FEATURE_KEY to Pair(
                ResourceObjectFeatureImport(1L, RESOURCE_SPEC_FEATURE_KEY, ResourceObjectFeatureImportStatusEnum.ENABLED.name, null, "", "", now, now, "N"),
                matchScopeData.copy(restrictions = listOf(
                        Restriction(site = "na610", clusterIdList = emptyList())
                    )
                )
            )
        )
        val workloadMetadataConstraint = WorkloadMetadataConstraint(
            appName = "normandy-test-app4", resourceGroup = "normandy-test-app4host", site = "na610", unit = "center", stage = "PUBLISH", clusterId = "cluster_a01"
        )
        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper()), recordPrivateCalls = true) {
            every {
                resourceObjectFeatureImportRepo.findById(any())
            } returns ResourceObjectFeatureImport(2L, RESOURCE_SPEC_FEATURE_KEY, ResourceObjectFeatureImportStatusEnum.ENABLED.name, null, "", "", now, now, "N")
        }
        resourceObjectFeatureService.matchScopeService = MatchScopeService(ObjectMapper())


        InternalPlatformDsl.dynamicCall(
            resourceObjectFeatureService,
            "computeFeatureImportMatchScopePriority",
            arrayOf(featureMap, matchScopeData, workloadMetadataConstraint, "envStackId")
        ) { mockk() }

        assertEquals(2L, featureMap[RESOURCE_SPEC_FEATURE_KEY]!!.first.id)
    }

    /**
     * 验证范围&限定条件重复
     * TODO 由于是针对私有方法验证，业务异常MatchScopeDataException被InvocationTargetException拦截（待解决）
     */
    @Test(expected = InvocationTargetException::class)
    fun testComputeFeatureImportMatchScopePriority_with_restriction_repeated() {
        val now = Date(Instant.now().toEpochMilli())
        val matchScopeData = MatchScopeDataDO(
            targetId = 1L,
            externalId = "1#2", creator = "10000", modifier = "10000",
            externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name,
            restrictions = listOf(
                Restriction(site = "na610", clusterIdList = emptyList())
            )
        )
        val featureMap = mutableMapOf(
            RESOURCE_SPEC_FEATURE_KEY to Pair(
                ResourceObjectFeatureImport(1L, RESOURCE_SPEC_FEATURE_KEY, ResourceObjectFeatureImportStatusEnum.ENABLED.name, null, "", "", now, now, "N"),
                matchScopeData.copy(restrictions = listOf(
                        Restriction(site = "na610", clusterIdList = emptyList())
                    )
                )
            )
        )
        val workloadMetadataConstraint = WorkloadMetadataConstraint(
            appName = "normandy-test-app4", resourceGroup = "normandy-test-app4host", site = "na610", unit = "center", stage = "PUBLISH", clusterId = "cluster_a01"
        )
        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper()), recordPrivateCalls = true) {
            every {
                resourceObjectFeatureImportRepo.findById(any())
            } returns ResourceObjectFeatureImport(2L, RESOURCE_SPEC_FEATURE_KEY, ResourceObjectFeatureImportStatusEnum.ENABLED.name, null, "", "", now, now, "N")
        }
        resourceObjectFeatureService.matchScopeService = MatchScopeService(ObjectMapper())

        InternalPlatformDsl.dynamicCall(
            resourceObjectFeatureService,
            "computeFeatureImportMatchScopePriority",
            arrayOf(featureMap, matchScopeData, workloadMetadataConstraint, "envStackId")
        ) { mockk() }
    }

    @Test
    fun `computeFeatureImportMatchScopePriority - diff restriction with envStackId`() {
        val now = Date(Instant.now().toEpochMilli())
        val matchScopeData = MatchScopeDataDO(
            targetId = 1L,
            externalId = "1#2", creator = "10000", modifier = "10000",
            externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name,
            restrictions = listOf(
                Restriction(envStackId = "envStackId")
            )
        )
        val featureMap = mutableMapOf(
            RESOURCE_SPEC_FEATURE_KEY to Pair(
                ResourceObjectFeatureImport(1L, RESOURCE_SPEC_FEATURE_KEY, ResourceObjectFeatureImportStatusEnum.ENABLED.name, null, "", "", now, now, "N"),
                matchScopeData.copy(restrictions = listOf(
                        Restriction(site = "na610", clusterIdList = emptyList())
                    )
                )
            )
        )
        val workloadMetadataConstraint = WorkloadMetadataConstraint(
            appName = "normandy-test-app4", resourceGroup = "normandy-test-app4host", site = "na610", unit = "center", stage = "PUBLISH", clusterId = "cluster_a01"
        )
        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper()), recordPrivateCalls = true) {
            every {
                resourceObjectFeatureImportRepo.findById(any())
            } returns ResourceObjectFeatureImport(2L, RESOURCE_SPEC_FEATURE_KEY, ResourceObjectFeatureImportStatusEnum.ENABLED.name, null, "", "", now, now, "N")
        }
        resourceObjectFeatureService.matchScopeService = MatchScopeService(ObjectMapper())


        InternalPlatformDsl.dynamicCall(
            resourceObjectFeatureService,
            "computeFeatureImportMatchScopePriority",
            arrayOf(featureMap, matchScopeData, workloadMetadataConstraint, "envStackId")
        ) { mockk() }

        assertEquals(2L, featureMap[RESOURCE_SPEC_FEATURE_KEY]!!.first.id)
    }

    @Test
    fun `deleteFeatureImportByMatchScope - not found`() {
        val req = ResourceObjectFeatureImportDeleteByMatchScopeReqDto(
            "featureKey", "m", mockk()
        )
        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())) {
            every {
                matchScopeService.findMatchScopeByExternalAndResourceObjectFeatureKey(
                    req.matchScope!!,
                    req.resourceObjectFeatureKey
                )
            } returns null
        }
        resourceObjectFeatureService.deleteFeatureImportByMatchScope(req)
    }

    @Test
    fun `deleteFeatureImportByMatchScope - delete match scope and feature import`() {
        val req = ResourceObjectFeatureImportDeleteByMatchScopeReqDto(
            "featureKey", "m", mockk()
        )
        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())) {
            every {
                matchScopeService.findMatchScopeByExternalAndResourceObjectFeatureKey(
                    req.matchScope!!,
                    req.resourceObjectFeatureKey
                )
            } returns mockk {
                every { id } returns 1L
                every { targetId } returns 2L
                every { modifier } returns "modifier"
            }

            justRun {
                modifierValidator(any(), any())
            }

            every { matchScopeService.deleteMatchScope(any(), req.modifier) } just runs
            every {
                matchScopeService.listByTarget(
                    2L,
                    MatchScopeTargetTypeEnum.ResourceObjectFeatureImport.name
                )
            } returns emptyList()
            every { resourceObjectFeatureImportRepo.deleteById(2L) } returns 1
        }

        resourceObjectFeatureService.deleteFeatureImportByMatchScope(req)
    }

    @Test
    fun `deleteFeatureImportByMatchScope - delete match scope but not feature import`() {
        val req = ResourceObjectFeatureImportDeleteByMatchScopeReqDto(
            "featureKey", "m", mockk()
        )
        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())) {
            every {
                matchScopeService.findMatchScopeByExternalAndResourceObjectFeatureKey(
                    req.matchScope!!,
                    req.resourceObjectFeatureKey
                )
            } returns mockk {
                every { id } returns 1L
                every { targetId } returns 2L
                every { modifier } returns "modifier"
            }
            justRun {
                modifierValidator(any(), any())
            }

            every { matchScopeService.deleteMatchScope(any(), req.modifier) } just runs

            every {
                matchScopeService.listByTarget(
                    2L,
                    MatchScopeTargetTypeEnum.ResourceObjectFeatureImport.name
                )
            } returns listOf(
                mockk { every { id } returns 11L },
                mockk { every { id } returns 22L },
            )
            every { resourceObjectFeatureImportRepo.deleteById(2L) } returns 1
        }

        resourceObjectFeatureService.deleteFeatureImportByMatchScope(req)
    }

    @Test
    fun `updateFeatureImportByMatchScope - not found`() {
        val req = ResourceObjectFeatureImportUpdateByMatchScopeReqDto(
            "featureKey", null, mapOf("a" to "b"), "m",
            MatchScopeDataReqDto("APPLICATION", "app_name")
        )
        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())) {
            every {
                matchScopeService.findMatchScopeByExternalAndResourceObjectFeatureKey(
                    req.matchScope,
                    req.resourceObjectFeatureKey
                )
            } returns null
        }

        exceptionRule.expect(ResourceObjectException::class.java)
        exceptionRule.expectMessage("资源对象特性导入不存在")

        resourceObjectFeatureService.updateFeatureImportByMatchScope(req)
    }

    @Test
    fun `updateFeatureImportByMatchScope - multi match scopes`() {
        val req = ResourceObjectFeatureImportUpdateByMatchScopeReqDto(
            "featureKey", null, mapOf("a" to "b"), "m",
            MatchScopeDataReqDto("APPLICATION", "app_name")
        )
        val featureImportId = 2L
        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())) {
            every {
                matchScopeService.findMatchScopeByExternalAndResourceObjectFeatureKey(
                    req.matchScope,
                    req.resourceObjectFeatureKey
                )
            } returns mockk {
                every { id } returns 1L
                every { targetId } returns featureImportId
            }

            every {
                matchScopeService.listByTarget(
                    featureImportId,
                    MatchScopeTargetTypeEnum.ResourceObjectFeatureImport.name
                )
            } returns listOf(mockk {
                every { id } returns 11L
            }, mockk {
                every { id } returns 12L
            })
        }

        exceptionRule.expect(ResourceObjectException::class.java)
        exceptionRule.expectMessage("资源对象特性导入存在多个匹配范围不允许更新")

        resourceObjectFeatureService.updateFeatureImportByMatchScope(req)
    }

    @Test
    fun `updateFeatureImportByMatchScope - match scope not expected`() {
        val req = ResourceObjectFeatureImportUpdateByMatchScopeReqDto(
            "featureKey", null, mapOf("a" to "b"), "m",
            MatchScopeDataReqDto("APPLICATION", "app_name")
        )
        val featureImportId = 2L
        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())) {
            every {
                matchScopeService.findMatchScopeByExternalAndResourceObjectFeatureKey(
                    req.matchScope,
                    req.resourceObjectFeatureKey
                )
            } returns mockk {
                every { id } returns 1L
                every { targetId } returns featureImportId
            }

            every {
                matchScopeService.listByTarget(
                    featureImportId,
                    MatchScopeTargetTypeEnum.ResourceObjectFeatureImport.name
                )
            } returns listOf(mockk {
                every { id } returns 11L
            })
        }

        exceptionRule.expect(IllegalStateException::class.java)
        exceptionRule.expectMessage("关联的匹配范围与预期不符")

        resourceObjectFeatureService.updateFeatureImportByMatchScope(req)
    }

    @Test
    fun updateFeatureImportByMatchScope() {
        val req = ResourceObjectFeatureImportUpdateByMatchScopeReqDto(
            "featureKey", null, mapOf("a" to "b"), "mm",
            MatchScopeDataReqDto("APPLICATION", "app_name")
        )
        val featureImportId = 2L
        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())) {
            every {
                matchScopeService.findMatchScopeByExternalAndResourceObjectFeatureKey(
                    req.matchScope,
                    req.resourceObjectFeatureKey
                )
            } returns mockk {
                every { id } returns 1L
                every { targetId } returns featureImportId
            }

            every {
                matchScopeService.listByTarget(
                    featureImportId,
                    MatchScopeTargetTypeEnum.ResourceObjectFeatureImport.name
                )
            } returns listOf(mockk {
                every { id } returns 1L
            })

            val featureImport = ResourceObjectFeatureImport(
                featureImportId,
                req.resourceObjectFeatureKey,
                "ENABLE",
                YamlUtils.dump(mapOf("c" to "d")),
                "c",
                "m"
            )
            every {
                resourceObjectFeatureImportRepo.findById(featureImportId)
            } returns featureImport

            every {
                resourceObjectFeatureImportRepo.updateById(
                    featureImportId,
                    "mm",
                    YamlUtils.dump(checkNotNull(req.paramMap)),
                    "ENABLE",
                    "0.0.1"
                )
            } returns 1
        }

        resourceObjectFeatureService.updateFeatureImportByMatchScope(req)
    }

    @Test
    fun `testGetResourceSpec while is daily resource group`() {
        val appName = "normandy-test-app4"
        val stage = "DAILY"
        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper()), recordPrivateCalls = true) {
            every {
                appCenterApi.getTestingResourceSpec(appName)
            }returns ResourceSpec(
                cpu = "2",
                memory = "4",
                disk = "60",
            )
        }

        val resourceSpec = InternalPlatformDsl.dynamicCall(
            resourceObjectFeatureService,
            "getResourceSpec",
            arrayOf(appName, stage)
        ) { mockk() } as Map<String, Map<String, Map<String, String>>>
        assertEquals("2", resourceSpec["resources"]!!["requests"]!!["cpu"])
        assertEquals("4", resourceSpec["resources"]!!["requests"]!!["memory"])
        assertEquals("60", resourceSpec["resources"]!!["requests"]!!["disk"])
        assertEquals("2", resourceSpec["resources"]!!["limits"]!!["cpu"])
        assertEquals("4", resourceSpec["resources"]!!["limits"]!!["memory"])
        assertEquals("60", resourceSpec["resources"]!!["limits"]!!["disk"])
    }

    @Test
    fun `testGetResourceSpec while is not daily resource group`() {
        val appName = "normandy-test-app4"
        val stage = "PUBLISH"
        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper()), recordPrivateCalls = true) {
            every {
                userLabelService.getAppResourceSpec(appName)
            }returns ResourceSpec(
                cpu = "2",
                memory = "4",
                disk = "60",
            )
        }

        val resourceSpec = InternalPlatformDsl.dynamicCall(
            resourceObjectFeatureService,
            "getResourceSpec",
            arrayOf(appName, stage)
        ) { mockk() } as Map<String, Map<String, Map<String, String>>>
        assertEquals("2", resourceSpec["resources"]!!["requests"]!!["cpu"])
        assertEquals("4", resourceSpec["resources"]!!["requests"]!!["memory"])
        assertEquals("60", resourceSpec["resources"]!!["requests"]!!["disk"])
        assertEquals("2", resourceSpec["resources"]!!["limits"]!!["cpu"])
        assertEquals("4", resourceSpec["resources"]!!["limits"]!!["memory"])
        assertEquals("60", resourceSpec["resources"]!!["limits"]!!["disk"])
    }

    @Test
    fun `testGetResourceSpec while is daily resource group and daily spec is null`() {
        val appName = "normandy-test-app4"
        val stage = "DAILY"
        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper()), recordPrivateCalls = true) {
            every {
                appCenterApi.getTestingResourceSpec(appName)
            }returns null
            every {
                userLabelService.getAppResourceSpec(appName)
            }returns ResourceSpec(
                cpu = "2",
                memory = "4",
                disk = "60",
            )
        }

        val resourceSpec = InternalPlatformDsl.dynamicCall(
            resourceObjectFeatureService,
            "getResourceSpec",
            arrayOf(appName, stage)
        ) { mockk() } as Map<String, Map<String, Map<String, String>>>
        assertEquals("2", resourceSpec["resources"]!!["requests"]!!["cpu"])
        assertEquals("4", resourceSpec["resources"]!!["requests"]!!["memory"])
        assertEquals("60", resourceSpec["resources"]!!["requests"]!!["disk"])
        assertEquals("2", resourceSpec["resources"]!!["limits"]!!["cpu"])
        assertEquals("4", resourceSpec["resources"]!!["limits"]!!["memory"])
        assertEquals("60", resourceSpec["resources"]!!["limits"]!!["disk"])
    }

    @Test
    fun test_groupTraitBySubmiiter() {
        val rawReq = testTraitModifyReq()
        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())) {
            every {
                resourceObjectFeatureRepo.findByResourceObjectFeatureKey("env-var")
            } returns listOf(
                ResourceObjectFeature(
                    1L,
                    "env-var",
                    "title",
                    "[{\"scene\":\"SCALE_OUT\",\"buildType\":\"CREATE\"},{\"scene\":\"SCALE_OUT\",\"buildType\":\"PATCH\"}]",
                    "",
                    "",
                    now,
                    now,
                    "Y",
                    "StatefulSet,CloneSet",
                    "INPUT",
                    "AFTER_VERSIONOUT",
                    null,
                    "TREE"
                )
            )
            every {
                resourceObjectFeatureRepo.findByResourceObjectFeatureKey("llm-engine-conf")
            } returns listOf(
                ResourceObjectFeature(
                    1L,
                    "llm-engine-conf",
                    "title",
                    "[{\"scene\":\"SCALE_OUT\",\"buildType\":\"CREATE\"},{\"scene\":\"SCALE_OUT\",\"buildType\":\"PATCH\"}]",
                    "",
                    "",
                    now,
                    now,
                    "Y",
                    "StatefulSet,CloneSet",
                    "INPUT",
                    "AFTER_VERSIONOUT",
                    null,
                    "TREE",
                    "0.0.1",
                    "LLM"
                )
            )
        }
        val group = resourceObjectFeatureService.groupTraitBySubmiiter(rawReq)
        assertEquals(
            "[{\"matchScope\":{\"externalId\":\"13ccc569-6de3-4609-99ab-f5cee32af59e\",\"externalType\":\"ENV_STACKID\"},\"addingTraitList\":[{\"traitKey\":\"llm-engine-conf\",\"status\":\"ENABLED\",\"formData\":{},\"version\":\"0.0.1\"}],\"createOrUpdateTraitList\":[{\"traitKey\":\"llm-engine-conf\",\"status\":\"ENABLED\",\"formData\":{},\"version\":\"0.0.1\"}],\"updatingTraitList\":[{\"traitKey\":\"llm-engine-conf\",\"status\":\"ENABLED\",\"formData\":{}}],\"deletingTraitList\":[{\"traitKey\":\"llm-engine-conf\"}],\"modifier\":\"changren\",\"submitters\":\"LLM\"},{\"matchScope\":{\"externalId\":\"13ccc569-6de3-4609-99ab-f5cee32af59e\",\"externalType\":\"ENV_STACKID\"},\"addingTraitList\":[{\"traitKey\":\"env-var\",\"status\":\"ENABLED\",\"formData\":{},\"version\":\"0.0.1\"}],\"createOrUpdateTraitList\":[{\"traitKey\":\"env-var\",\"status\":\"ENABLED\",\"formData\":{},\"version\":\"0.0.1\"}],\"updatingTraitList\":[{\"traitKey\":\"env-var\",\"status\":\"ENABLED\",\"formData\":{}}],\"deletingTraitList\":[{\"traitKey\":\"env-var\"}],\"modifier\":\"changren\",\"submitters\":\"SYSTEM\"}]",
            JsonUtils.writeValueAsString(group)
        )


    }

    @Test
    fun test_getTraitKeyList() {
        val rawReq = testTraitModifyReqDto()
        val list = ResourceObjectFeatureService.getTraitKeyList(rawReq)
        assertEquals("[\"llm-engine-conf\",\"env-var\"]", JsonUtils.writeValueAsString(list))

    }

    @Test
    fun test_buildActionType() {
        val rawReq = testTraitModifyReqDto()
        val type = ResourceObjectFeatureService.buildActionType(rawReq)
        assertEquals("CREATE,UPDATE,UPDATE,DELETE", type)

    }

    private fun testTraitModifyReq() = JsonUtils.readValue(
        """
                {
                  "matchScope": {
                    "externalId": "13ccc569-6de3-4609-99ab-f5cee32af59e",
                    "externalType": "ENV_STACKID"
                  },
                  "modifier": "changren",
                  "submitters": "LLM",
                  "updatingTraitList": [
                    {
                      "formData": {},
                      "status": "ENABLED",
                      "traitKey": "llm-engine-conf"
                    },
                    {
                      "formData": {},
                      "status": "ENABLED",
                      "traitKey": "env-var"
                    }
                  ],
                  "createOrUpdateTraitList": [
                    {
                      "formData": {},
                      "status": "ENABLED",
                      "traitKey": "llm-engine-conf"
                    },
                    {
                      "formData": {},
                      "status": "ENABLED",
                      "traitKey": "env-var"
                    }
                  ],
                  "deletingTraitList": [
                    {
                      "traitKey": "llm-engine-conf"
                    },
                    {
                      "traitKey": "env-var"
                    }
                  ],
                  "addingTraitList": [
                    {
                      "formData": {},
                      "status": "ENABLED",
                      "traitKey": "llm-engine-conf"
                    },
                    {
                      "formData": {},
                      "status": "ENABLED",
                      "traitKey": "env-var"
                    }
                  ]
                }
            """.trimIndent(), TraitModifyReq::class.java
    )

    private fun testTraitModifyReqDto() = JsonUtils.readValue(
        """
                {
                  "matchScope": {
                    "externalId": "13ccc569-6de3-4609-99ab-f5cee32af59e",
                    "externalType": "ENV_STACKID"
                  },
                  "modifier": "changren",
                  "submitters": "LLM",
                  "updatingTraitList": [
                    {
                      "formData": {},
                      "status": "ENABLED",
                      "traitKey": "llm-engine-conf"
                    },
                    {
                      "formData": {},
                      "status": "ENABLED",
                      "traitKey": "env-var"
                    }
                  ],
                  "createOrUpdateTraitList": [
                    {
                      "formData": {},
                      "status": "ENABLED",
                      "traitKey": "llm-engine-conf"
                    },
                    {
                      "formData": {},
                      "status": "ENABLED",
                      "traitKey": "env-var"
                    }
                  ],
                  "deletingTraitList": [
                    {
                      "traitKey": "llm-engine-conf"
                    },
                    {
                      "traitKey": "env-var"
                    }
                  ],
                  "addingTraitList": [
                    {
                      "formData": {},
                      "status": "ENABLED",
                      "traitKey": "llm-engine-conf"
                    },
                    {
                      "formData": {},
                      "status": "ENABLED",
                      "traitKey": "env-var"
                    }
                  ]
                }
            """.trimIndent(), TraitModifyReqDto::class.java
    )

    @Test
    fun `test listVersionFeatureImportSpec with both userTraits and configTraits`() {
        val now = Date(Instant.now().toEpochMilli())
        val appName = "testApp"
        val envStackId = "stack123"
        val protocol = "testProtocol"
        val userTraits = listOf(
            UserTrait(
                key = "trait1",
                content = mapOf("param1" to "value1"),
                modifier = "user1",
            ),
            UserTrait(
                key = "configTrait",
                content = mapOf("configParam" to "newConfigValue"),
                modifier = "user1",
                version = "tgi-1.0.0"
            ),
        )
        val systemInputParams = mapOf("sys1" to "val1")

        val configFeature = ResourceObjectFeatureImport(
            id = 1,
            resourceObjectFeatureKey = "configTrait",
            paramMap = YamlUtils.dump(mapOf("configParam" to "configValue")),
            status = "ENABLE",
            creator = "system",
            modifier = "system",
            gmtCreate = now,
            gmtModified = now,
            version = "tgi-1.0.0"
        )

        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())) {
            every { extractVersionTraitMap(appName, envStackId) } returns mutableMapOf(
                "configTrait" to Pair(configFeature, mockk())
            )
            every { filteredVersionFeatureImport(any(), any()) } returns listOf(configFeature)
            every {
                assembleSpecByFeatureImport(
                    any(),
                    protocol,
                    null,
                    systemInputParams
                )
            } returns emptyList()
        }

        resourceObjectFeatureService.listVersionFeatureImportSpec(
            appName = appName,
            resourceObjectSceneEnum = ResourceObjectSceneEnum.DEPLOY,
            resourceObjectBuildTypeEnum = ResourceObjectBuildTypeEnum.PATCH,
            protocol = protocol,
            version = null,
            userTraits = userTraits,
            systemInputParams = systemInputParams,
            envStackId = envStackId
        )


        verify {
            resourceObjectFeatureService.extractVersionTraitMap(appName, envStackId)
            resourceObjectFeatureService.filteredVersionFeatureImport(any(), protocol)
            resourceObjectFeatureService.assembleSpecByFeatureImport(
                match { featureImports ->
                    featureImports.size == 2 &&
                    featureImports.firstOrNull {
                        it.resourceObjectFeatureKey == "trait1"
                    }!!.paramMap == "param1: value1\n" &&
                    featureImports.firstOrNull {
                        it.resourceObjectFeatureKey == "configTrait"
                    }!!.paramMap == "configParam: newConfigValue\n"
                }, protocol, null, systemInputParams)
        }
    }

    @Test
    fun testModifierValidator_SameModifiers_NoException() {
        val originalModifier = "user1"
        val newModifier = "user1"

        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper()))
        assertDoesNotThrow {
            resourceObjectFeatureService.modifierValidator(originalModifier, newModifier)
        }
    }

    @Test
    fun testModifierValidator_DifferentModifiers_NewModifierNotLowRank_NoException() {
        val originalModifier = "user1"
        val newModifier = "user2"
        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())) {
            every { commonProperties.contains("LOW_RANK_MODIFIER", newModifier) } returns false
        }

        assertDoesNotThrow {
            resourceObjectFeatureService.modifierValidator(originalModifier, newModifier)
        }
    }

    @Test
    fun testModifierValidator_DifferentModifiers_NewModifierLowRank_Exception() {
        val originalModifier = "user1"
        val newModifier = "user2"
        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())) {
            every { commonProperties.contains("LOW_RANK_MODIFIER", newModifier) } returns true
        }

        val exception = assertThrows(ResourceObjectException::class.java) {
            resourceObjectFeatureService.modifierValidator(originalModifier, newModifier)
        }

        assertTrue(exception.message!!.contains("操作人不一致且新操作人权限低"))
        assertTrue(exception.message!!.contains("原操作人:$originalModifier"))
        assertTrue(exception.message!!.contains("新操作人:$newModifier"))
    }

    @Test
    fun `test listVersionFeatureImportSpec with both userTraits and configTraits and same key different version`() {
        val now = Date(Instant.now().toEpochMilli())
        val appName = "testApp"
        val envStackId = "stack123"
        val protocol = "testProtocol"
        val userTraits = listOf(
            UserTrait(
                key = "trait1",
                content = mapOf("param1" to "value1"),
                modifier = "user1",
            ),
            UserTrait(
                key = "configTrait",
                content = mapOf("configParam" to "newConfigValue"),
                modifier = "user1",
                version = "tgi-1.0.0"
            ),
        )
        val systemInputParams = mapOf("sys1" to "val1")

        val configFeature = ResourceObjectFeatureImport(
            id = 1,
            resourceObjectFeatureKey = "configTrait",
            paramMap = YamlUtils.dump(mapOf("configParam" to "configValue")),
            status = "ENABLE",
            creator = "system",
            modifier = "system",
            gmtCreate = now,
            gmtModified = now,
            version = "vllm-1.0.0"
        )

        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())) {
            every { extractVersionTraitMap(appName, envStackId) } returns mutableMapOf(
                "configTrait" to Pair(configFeature, mockk())
            )
            every { filteredVersionFeatureImport(any(), any()) } returns listOf(configFeature)
            every {
                assembleSpecByFeatureImport(
                    any(),
                    protocol,
                    null,
                    systemInputParams
                )
            } returns emptyList()
        }

        resourceObjectFeatureService.listVersionFeatureImportSpec(
            appName = appName,
            resourceObjectSceneEnum = ResourceObjectSceneEnum.DEPLOY,
            resourceObjectBuildTypeEnum = ResourceObjectBuildTypeEnum.PATCH,
            protocol = protocol,
            version = null,
            userTraits = userTraits,
            systemInputParams = systemInputParams,
            envStackId = envStackId
        )


        verify {
            resourceObjectFeatureService.extractVersionTraitMap(appName, envStackId)
            resourceObjectFeatureService.filteredVersionFeatureImport(any(), protocol)
            resourceObjectFeatureService.assembleSpecByFeatureImport(
                match { featureImports ->
                    featureImports.size == 2 &&
                            featureImports.firstOrNull {
                                it.resourceObjectFeatureKey == "trait1"
                            }!!.paramMap == "param1: value1\n" &&
                            featureImports.firstOrNull {
                                it.resourceObjectFeatureKey == "configTrait"
                            }!!.paramMap == "configParam: newConfigValue\n"
                }, protocol, null, systemInputParams)
        }
    }

    @Test
    fun `test filteredVersionFeatureImport multiple version for single trait`() {
        val now = Date(Instant.now().toEpochMilli())
        val protocol = "StatefulSet"

        val featureMap = mapOf(
            "feature1" to Pair(
                ResourceObjectFeatureImport(
                    id = 1,
                    resourceObjectFeatureKey = "feature1",
                    status = "ENABLED",
                    paramMap = "{}",
                    creator = "admin",
                    modifier = "admin",
                    version = "1.0.0",
                    gmtCreate = now,
                    gmtModified = now
                ),
                MatchScopeDataDO(
                    externalId = "test",
                    externalType = "APPLICATION",
                    creator = "admin",
                    modifier = "admin"
                )
            )
        )

        val resourceObjectFeatures = listOf(
            ResourceObjectFeature(
                resourceObjectFeatureKey = "feature1",
                feasibleProtocols = "StatefulSet",
                useScope = "[]",
                effectiveStage = "AFTER_VERSIONOUT",
                version = "1.0.0",
                creator = "admin",
                modifier = "admin",
                gmtCreate = now,
                gmtModified = now,
                isDeleted = "N",
                displayTheme = "TREE",
                title = "feature1",
                type = "INPUY",
            ),
            ResourceObjectFeature(
                resourceObjectFeatureKey = "feature1",
                feasibleProtocols = "StatefulSet",
                useScope = "[]",
                effectiveStage = "AFTER_VERSIONOUT",
                version = "2.0.0",
                creator = "admin",
                modifier = "admin",
                gmtCreate = now,
                gmtModified = now,
                isDeleted = "N",
                displayTheme = "TREE",
                title = "feature1",
                type = "INPUY",
            )
        )

        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())) {
            every {
                resourceObjectFeatureRepo.findByResourceObjectFeatureKeyList(any())
            } returns resourceObjectFeatures
        }

        val result = resourceObjectFeatureService.filteredVersionFeatureImport(featureMap, protocol)

        assertEquals(1, result.size)
    }

    @Test
    fun `test pageGetCombinedFeatureImportByKeys with illegal trait`() {
        val req = ResourceObjectFeatureImportAsiAwareReqDto(
            traitKeyList = listOf("illegal"),
            nextToken = "0",
            pageSize = 100
        )

        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())) {
            every {
                commonProperties.get(any())
            } returns listOf("legal")
        }

        softly.assertThatThrownBy {
            resourceObjectFeatureService.pageGetCombinedFeatureImportByKeys(req)
        }
    }

    @Test
    fun `test pageGetCombinedFeatureImportByKeys success`() {
        val req = ResourceObjectFeatureImportAsiAwareReqDto(
            traitKeyList = listOf("legal"),
            nextToken = "0",
            pageSize = 100
        )

        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())) {
            every {
                commonProperties.get(any())
            } returns listOf("legal")

            every {
                resourceObjectFeatureImportRepo.findCombineByKeys(
                    any(), any(), any(), any(), any()
                )
            } returns listOf(
                manufacturePojo(ResourceObjectFeatureImportWithMatchScopeCombine::class.java)
                    .copy(
                        paramMap = YamlUtils.dump(mapOf("c" to "d")),
                        restrictions = listOf(manufacturePojo(Restriction::class.java)).toJson(),
                        importId = 1
                    )
            )

            every {
                featureImportHookManager.preProcess(any())
            } returns manufacturePojo(ResourceObjectFeatureImport::class.java).copy(paramMap = YamlUtils.dump(mapOf("c" to "d")))
        }

        val result = resourceObjectFeatureService.pageGetCombinedFeatureImportByKeys(req)
        softly.assertThat(result.nextToken).isNull()
        softly.assertThat(!result.data!!.first().formData.isNullOrEmpty()).isTrue
    }

    @Test
    public fun filterByType_typeIsAll_returnsTrue() {
        val service = ResourceObjectFeatureService(ObjectMapper())
        val feature = mockk<ResourceObjectFeature> {

        }

        val result = service.filterByType(feature, ResourceObjectFeatureTypeEnum.ALL)

        Assert.assertTrue(result)
    }

    @Test
    public fun filterByType_typeIsEditable_returnsTrue() {
        val service = ResourceObjectFeatureService(ObjectMapper())
        val feature = mockk<ResourceObjectFeature> {
            every { jsonSchema } returns "json"
        }

        val result = service.filterByType(feature, ResourceObjectFeatureTypeEnum.EDITABLE)

        Assert.assertTrue(result)
    }

    @Test
    public fun filterByType_typeIsEditable_returnsFalse() {
        val service = ResourceObjectFeatureService(ObjectMapper())
        val feature = mockk<ResourceObjectFeature> {
            every { jsonSchema } returns null
        }

        val result = service.filterByType(feature, ResourceObjectFeatureTypeEnum.EDITABLE)

        Assert.assertFalse(result)
    }

    @Test
    fun `test getFeatureImportByAppName with enabled features`() {
        val now = Date(Instant.now().toEpochMilli())
        val appName = "testApp"
        val matchScopeData = MatchScopeDataDO(
            id = 1,
            targetId = 1,
            externalId = appName,
            externalType = "APPLICATION",
            creator = "admin",
            modifier = "admin"
        )
        val feature = ResourceObjectFeature(
            resourceObjectFeatureKey = "feature1",
            effectiveStage = "DURING_VERSIONOUT",
            type = "INPUT",
            jsonSchema = "schema",
            version = "1.0.0",
            creator = "admin",
            modifier = "admin",
            gmtCreate = now,
            gmtModified = now,
            displayTheme = "TREE",
            title = "Feature 1",
            feasibleProtocols = "StatefulSet",
            useScope = "[]",
        )
        val featureImport = ResourceObjectFeatureImport(
            id = 1,
            resourceObjectFeatureKey = "feature1",
            status = "ENABLED",
            paramMap = "param1: value1",
            creator = "admin",
            modifier = "admin",
            gmtCreate = now,
            gmtModified = now,
            version = "1.0.0"
        )

        val dispatchLabelValue = ConfigDispatchLabelValueWithMetadata(
            id = 1,
            labelCode = "test",
            labelValue = "1",
            labelType = "constraints",
            appName = "app1",
            creator = "test",
            modifier = "test",
            labelName = "test",
        )

        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())) {
            every { matchScopeService.findMatchScopesByAppNameAndResourceGroupsForResourceObjectFeatureImport(any(), any()) } returns listOf(matchScopeData)
            every { resourceObjectFeatureImportRepo.findById(any()) } returns featureImport
            every { resourceObjectFeatureRepo.findByResourceObjectFeatureKeyAndVersion(any(), any()) } returns feature
            every { getGroupNameByAppNameFromSkyline(any()) } returns listOf("group1")
            every { dispatchLabelService.getLabelValue(any()) } returns listOf(dispatchLabelValue)
            every { commonProperties.firstOrNull(LOCAL_APP_2_GROUP) } returns "false"
        }

        val result = resourceObjectFeatureService.getFeatureImportByAppName(
            ResourceObjectFeatureImportGetByAppNameReqDto(
                appName = appName,
                traitEffectiveStageFilter = ResourceObjectFeatureEffectiveStageEnum.DURING_VERSIONOUT,
                includingSystem = false,
                includingLegacy = true
            )
        )

        assertEquals(2, result.size)
        assertEquals(featureImport.id, result[0].id)
        assertEquals(featureImport.resourceObjectFeatureKey, result[0].resourceObjectFeatureKey)
        assertEquals(dispatchLabelValue.labelCode, result[1].resourceObjectFeatureKey)

        val result2 = resourceObjectFeatureService.getFeatureImportByAppName(
            ResourceObjectFeatureImportGetByAppNameReqDto(
                appName = appName,
                traitEffectiveStageFilter = ResourceObjectFeatureEffectiveStageEnum.DURING_VERSIONOUT,
                includingSystem = false,
                includingLegacy = false
            )
        )

        assertEquals(1, result2.size)
        assertEquals(featureImport.id, result[0].id)
        assertEquals(featureImport.resourceObjectFeatureKey, result[0].resourceObjectFeatureKey)
    }

    @Test
    fun `updateFeature -- preserves existing values when update fields are null or empty`() {
        val existingFeature = ResourceObjectFeature(
            id = 1L,
            resourceObjectFeatureKey = "test-feature",
            title = "Original Title",
            useScope = "[{\"scene\":\"SCALE_OUT\",\"buildType\":\"CREATE\"}]",
            creator = "000001",
            modifier = "000001",
            gmtCreate = now,
            gmtModified = now,
            isDeleted = "N",
            feasibleProtocols = "StatefulSet,CloneSet",
            jsonSchema = "{\"properties\": {}}",
            type = "INPUT",
            effectiveStage = "AFTER_VERSIONOUT",
            displayTheme = "TREE",
            version = "1.0.0",
            submitters = "SYSTEM"
        )

        val updateDto = ResourceObjectFeatureUpdateReqDto(
            id = 1L,
            resourceObjectFeatureKey = "test-feature",
            title = "Updated Title",
            useScope = emptyList(),  // Empty list should preserve original value
            modifier = "000002",
            feasibleProtocols = null,  // Null should preserve original value
            jsonSchema = null,         // Null should preserve original value
            type = null,               // Null should preserve original value
            effectiveStage = null,     // Null should preserve original value
            displayTheme = null,       // Null should preserve original value
            matchScopeData = null,
            version = null,            // Null should preserve original value
            submitters = null          // Null should preserve original value
        )

        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper()))
        
        // Mock repository behavior
        every { resourceObjectFeatureService.resourceObjectFeatureRepo.findById(1L) } returns existingFeature
        every { 
            resourceObjectFeatureService.resourceObjectFeatureRepo.updateById(
                id = 1L,
                title = "Updated Title",
                useScope = existingFeature.useScope, // Should use existing value
                modifier = "000002",
                feasibleProtocols = existingFeature.feasibleProtocols, // Should use existing value
                jsonSchema = existingFeature.jsonSchema, // Should use existing value
                type = existingFeature.type, // Should use existing value
                effectiveStage = existingFeature.effectiveStage, // Should use existing value
                displayTheme = existingFeature.displayTheme, // Should use existing value
                version = existingFeature.version, // Should use existing value
                submitters = existingFeature.submitters // Should use existing value
            )
        } returns 1
        
        // Mock matchScopeService to avoid NullPointerException
        resourceObjectFeatureService.matchScopeService = mockk {
            every { deleteMatchScopeByTarget(any(), any(), any()) } returns Unit
        }

        // Execute the update
        resourceObjectFeatureService.updateFeature(updateDto)

        // Verify the updateById was called with correct parameters
        verify {
            resourceObjectFeatureService.resourceObjectFeatureRepo.updateById(
                id = 1L,
                title = "Updated Title",
                useScope = existingFeature.useScope,
                modifier = "000002",
                feasibleProtocols = existingFeature.feasibleProtocols,
                jsonSchema = existingFeature.jsonSchema,
                type = existingFeature.type,
                effectiveStage = existingFeature.effectiveStage,
                displayTheme = existingFeature.displayTheme,
                version = existingFeature.version,
                submitters = existingFeature.submitters
            )
        }
    }

    @Test
    fun `updateFeature -- updates values when non-null fields are provided`() {
        val existingFeature = ResourceObjectFeature(
            id = 1L,
            resourceObjectFeatureKey = "test-feature",
            title = "Original Title",
            useScope = "[{\"scene\":\"SCALE_OUT\",\"buildType\":\"CREATE\"}]",
            creator = "000001",
            modifier = "000001",
            gmtCreate = now,
            gmtModified = now,
            isDeleted = "N",
            feasibleProtocols = "StatefulSet,CloneSet",
            jsonSchema = "{\"properties\": {}}",
            type = "INPUT",
            effectiveStage = "AFTER_VERSIONOUT",
            displayTheme = "TREE",
            version = "1.0.0",
            submitters = "SYSTEM"
        )

        val newUseScope = listOf(ResourceObjectFeatureUseScope(ResourceObjectSceneEnum.DEPLOY, ResourceObjectBuildTypeEnum.PATCH))
        val newUseScopeJson = JsonUtils.writeValueAsString(newUseScope)

        val updateDto = ResourceObjectFeatureUpdateReqDto(
            id = 1L,
            resourceObjectFeatureKey = "test-feature",
            title = "Updated Title",
            useScope = newUseScope,  // Non-empty list should update
            modifier = "000002",
            feasibleProtocols = "NewProtocol",  // Non-null should update
            jsonSchema = "{\"new\": true}",     // Non-null should update
            type = ResourceObjectFeatureTypeEnum.EDITABLE,  // Non-null should update
            effectiveStage = ResourceObjectFeatureEffectiveStageEnum.DURING_VERSIONOUT, // Non-null should update
            displayTheme = ResourceObjectFeatureDisplayThemeEnum.TREE,  // Non-null should update
            matchScopeData = null,
            version = "2.0.0",        // Non-null should update
            submitters = "NEW_SYSTEM" // Non-null should update
        )

        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper()))
        
        // Mock repository behavior
        every { resourceObjectFeatureService.resourceObjectFeatureRepo.findById(1L) } returns existingFeature
        every { 
            resourceObjectFeatureService.resourceObjectFeatureRepo.updateById(
                id = 1L,
                title = "Updated Title",
                useScope = newUseScopeJson,
                modifier = "000002",
                feasibleProtocols = "NewProtocol",
                jsonSchema = "{\"new\": true}",
                type = "EDITABLE",
                effectiveStage = "DURING_VERSIONOUT",
                displayTheme = "TREE",
                version = "2.0.0",
                submitters = "NEW_SYSTEM"
            )
        } returns 1
        
        // Mock matchScopeService to avoid NullPointerException
        resourceObjectFeatureService.matchScopeService = mockk {
            every { deleteMatchScopeByTarget(any(), any(), any()) } returns Unit
        }

        // Execute the update
        resourceObjectFeatureService.updateFeature(updateDto)

        // Verify the updateById was called with correct parameters
        verify {
            resourceObjectFeatureService.resourceObjectFeatureRepo.updateById(
                id = 1L,
                title = "Updated Title",
                useScope = newUseScopeJson,
                modifier = "000002", 
                feasibleProtocols = "NewProtocol",
                jsonSchema = "{\"new\": true}",
                type = "EDITABLE",
                effectiveStage = "DURING_VERSIONOUT",
                displayTheme = "TREE",
                version = "2.0.0",
                submitters = "NEW_SYSTEM"
            )
        }
    }






}