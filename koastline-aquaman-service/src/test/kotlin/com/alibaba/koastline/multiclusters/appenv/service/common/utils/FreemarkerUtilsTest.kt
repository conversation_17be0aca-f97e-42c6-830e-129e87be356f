package com.alibaba.koastline.multiclusters.appenv.service.common.utils

import com.alibaba.koastline.multiclusters.common.utils.FreemarkerUtils
import com.alibaba.koastline.multiclusters.common.utils.YamlUtils
import org.junit.Test
import kotlin.test.assertEquals

class FreemarkerUtilsTest {

    @Test
    fun testParseTemplate_with_full_params() {
        val tpl = """
spec:
  containers:
    - name: main
      resources:
        requests:
          cpu: ${'$'}{user.resources.requests.cpu}
          memory: ${'$'}{user.resources.requests.memory}
          ephemeral-storage: ${'$'}{user.resources.requests.disk}
          <#if user.resources.requests.gpu??>
          nvidia.com/gpu: ${'$'}{user.resources.requests.gpu}
          </#if>
        limits:
          cpu: ${'$'}{user.resources.limits.cpu}
          memory: ${'$'}{user.resources.limits.memory}
          ephemeral-storage: ${'$'}{user.resources.limits.disk}
          <#if user.resources.limits.gpu??>
          nvidia.com/gpu: ${'$'}{user.resources.limits.gpu}
          </#if>"""
        val paramMap = YamlUtils.load("""
            resources:
                requests:
                  cpu: '4'
                  memory: 8Gi
                  disk: 60Gi
                  gpu: '1'
                limits:
                  cpu: '4'
                  memory: 8Gi
                  disk: 60Gi
                  gpu: '1'
        """)
        assertEquals("""
spec:
  containers:
    - name: main
      resources:
        requests:
          cpu: 4
          memory: 8Gi
          ephemeral-storage: 60Gi
          nvidia.com/gpu: 1
        limits:
          cpu: 4
          memory: 8Gi
          ephemeral-storage: 60Gi
          nvidia.com/gpu: 1
""",FreemarkerUtils.parseTemplate(tpl, mapOf("user" to paramMap)))
    }

    @Test
    fun test_resourceSpec_number_presentation() {
        val tpl = """
<#setting number_format="computer">
spec:
  template:
    metadata:
      annotations:
        sigma.ali/app-storage-size: "${'$'}{user.resources.requests.disk}"
    spec:
      containers:
        - name: main
          resources:
            requests:
              cpu: "${'$'}{user.resources.requests.cpu}"
              memory: "${'$'}{user.resources.requests.memory}"
              <#if user.resources.requests.gpu?? && user.resources.requests.gpu?trim?length gt 0>
              alibabacloud.com/gpu: "${'$'}{((user.resources.requests.gpu?number)*100)?int}"
              </#if>
            limits:
              cpu: "${'$'}{user.resources.limits.cpu}"
              memory: "${'$'}{user.resources.limits.memory}"
              <#if user.resources.limits.gpu?? && user.resources.requests.gpu?trim?length gt 0>
              alibabacloud.com/gpu: "${'$'}{((user.resources.limits.gpu?number)*100)?int}"
              </#if>
              """.trimIndent()
        val paramMap = YamlUtils.load(
            """
resources:
  requests:
    cpu: '32'
    memory: '256'
    disk: '60'
    gpu: '16'
  limits:
    cpu: '32'
    memory: '256'
    disk: '60'
    gpu: '16'
        """
        )
        assertEquals(
            """spec:
  template:
    metadata:
      annotations:
        sigma.ali/app-storage-size: "60"
    spec:
      containers:
        - name: main
          resources:
            requests:
              cpu: "32"
              memory: "256"
              alibabacloud.com/gpu: "1600"
            limits:
              cpu: "32"
              memory: "256"
              alibabacloud.com/gpu: "1600"
""", FreemarkerUtils.parseTemplate(tpl, mapOf("user" to paramMap))
        )
        val tpl2 = """
spec:
  template:
    metadata:
      annotations:
        sigma.ali/app-storage-size: "${'$'}{user.resources.requests.disk}"
    spec:
      containers:
        - name: main
          resources:
            requests:
              cpu: "${'$'}{user.resources.requests.cpu}"
              memory: "${'$'}{user.resources.requests.memory}"
              <#if user.resources.requests.gpu?? && user.resources.requests.gpu?trim?length gt 0>
              alibabacloud.com/gpu: "${'$'}{((user.resources.requests.gpu?number)*100)?int}"
              </#if>
            limits:
              cpu: "${'$'}{user.resources.limits.cpu}"
              memory: "${'$'}{user.resources.limits.memory}"
              <#if user.resources.limits.gpu?? && user.resources.requests.gpu?trim?length gt 0>
              alibabacloud.com/gpu: "${'$'}{((user.resources.limits.gpu?number)*100)?int}"
              </#if>
              """.trimIndent()
        assertEquals(
            """spec:
  template:
    metadata:
      annotations:
        sigma.ali/app-storage-size: "60"
    spec:
      containers:
        - name: main
          resources:
            requests:
              cpu: "32"
              memory: "256"
              alibabacloud.com/gpu: "1,600"
            limits:
              cpu: "32"
              memory: "256"
              alibabacloud.com/gpu: "1,600"
""", FreemarkerUtils.parseTemplate(tpl2, mapOf("user" to paramMap))
        )
    }

    @Test
    fun testParseTemplate_with_check_template_condition() {
        val tpl = """
spec:
  containers:
    - name: main
      resources:
        requests:
          cpu: ${'$'}{user.resources.requests.cpu}
          memory: ${'$'}{user.resources.requests.memory}
          ephemeral-storage: ${'$'}{user.resources.requests.disk}
          <#if user.resources.requests.gpu??>
          nvidia.com/gpu: ${'$'}{user.resources.requests.gpu}
          </#if>
        limits:
          cpu: ${'$'}{user.resources.limits.cpu}
          memory: ${'$'}{user.resources.limits.memory}
          ephemeral-storage: ${'$'}{user.resources.limits.disk}
          <#if user.resources.limits.gpu??>
          nvidia.com/gpu: ${'$'}{user.resources.limits.gpu}
          </#if>"""
        val paramMap = YamlUtils.load("""
            resources:
                requests:
                  cpu: '4'
                  memory: 8Gi
                  disk: 60Gi
                limits:
                  cpu: '4'
                  memory: 8Gi
                  disk: 60Gi
        """)
        assertEquals("""
spec:
  containers:
    - name: main
      resources:
        requests:
          cpu: 4
          memory: 8Gi
          ephemeral-storage: 60Gi
        limits:
          cpu: 4
          memory: 8Gi
          ephemeral-storage: 60Gi
""",FreemarkerUtils.parseTemplate(tpl, mapOf("user" to paramMap)))
    }

    @Test
    fun testParseTemplate_with_empty_map() {
        val tpl = """
<#if user.customSystemProperties?has_content>
customSystemProperties: 
<#list user.customSystemProperties as key, value>
  ${'$'}{key}: ${'$'}{value}
</#list>
</#if>
"""
        val paramMap = YamlUtils.load("""{}""".trimIndent())
        assertEquals("""
""",FreemarkerUtils.parseTemplate(tpl, mapOf("user" to paramMap)))
    }


    @Test
    fun innerYamlPodTest(){
        val paddingNumber = 1
        val originalYaml = """
spec:
  containers:
    - name: main
      resources:
        requests:
          cpu: 4
          memory: 8Gi
          ephemeral-storage: 60Gi
        limits:
          cpu: 4
          memory: 8Gi
          ephemeral-storage: 60Gi
        """.trimIndent()

        val finalYaml = """
spec:
    containers:
      - name: main
        resources:
          requests:
            cpu: 4
            memory: 8Gi
            ephemeral-storage: 60Gi
          limits:
            cpu: 4
            memory: 8Gi
            ephemeral-storage: 60Gi
        """.trimIndent()

        val trimYaml = FreemarkerUtils.innerYamlPad(paddingNumber, originalYaml)
        assertEquals(trimYaml, finalYaml)
    }
}